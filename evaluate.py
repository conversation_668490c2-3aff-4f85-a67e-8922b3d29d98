#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模型评估脚本
计算详细的评估指标
"""

import torch
import numpy as np
import json
import pickle
import logging
import os
import argparse
from datetime import datetime
from typing import Dict, List, Tuple
from sklearn.metrics import (
    accuracy_score, precision_recall_fscore_support,
    classification_report, confusion_matrix
)
# import matplotlib.pyplot as plt
# import seaborn as sns
from transformers import AutoTokenizer

from src.models.hierarchical_model import HierarchicalQwenModel
from src.data.data_loader import create_data_loaders

def setup_evaluation_logging():
    """设置评估日志"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_dir = f"/data/TACL_chinese1_reset_v2/results/evaluation/evaluation_{timestamp}"
    os.makedirs(log_dir, exist_ok=True)

    log_file = os.path.join(log_dir, "evaluation.log")
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )

    logging.info(f"📊 评估日志目录: {log_dir}")
    return log_dir

class ModelEvaluator:
    """模型评估器"""

    def __init__(self, model_path: str, checkpoint_path: str,
                 num_classes_per_level: List[int], label_mappings: Dict):
        self.model_path = model_path
        self.checkpoint_path = checkpoint_path
        self.num_classes_per_level = num_classes_per_level
        self.label_mappings = label_mappings
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

        # 加载模型
        self.model = self._load_model()
        self.tokenizer = AutoTokenizer.from_pretrained(model_path)
        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token

    def _load_model(self):
        """加载训练好的模型"""
        model = HierarchicalQwenModel(
            model_path=self.model_path,
            num_classes_per_level=self.num_classes_per_level
        )

        # 加载检查点
        checkpoint = torch.load(self.checkpoint_path, map_location=self.device, weights_only=False)
        model.load_state_dict(checkpoint['model_state_dict'])
        model.to(self.device)
        model.eval()

        return model

    def evaluate_on_dataset(self, data_loader) -> Tuple[List[List[int]], List[List[int]]]:
        """在数据集上进行评估"""
        all_predictions = [[] for _ in range(len(self.num_classes_per_level))]
        all_targets = [[] for _ in range(len(self.num_classes_per_level))]

        # 清理GPU内存
        if torch.cuda.is_available():
            torch.cuda.empty_cache()

        with torch.no_grad():
            for batch_idx, batch in enumerate(data_loader):
                try:
                    input_ids = batch['input_ids'].to(self.device)
                    attention_mask = batch['attention_mask'].to(self.device)
                    labels = [batch['labels'][:, i].to(self.device) for i in range(len(self.num_classes_per_level))]

                    outputs = self.model(input_ids, attention_mask)
                    logits_list = outputs['logits']

                    # 收集预测和真实标签
                    for i, (logits, target) in enumerate(zip(logits_list, labels)):
                        predictions = torch.argmax(logits, dim=-1)
                        all_predictions[i].extend(predictions.cpu().numpy())
                        all_targets[i].extend(target.cpu().numpy())

                    # 定期清理GPU内存
                    if batch_idx % 10 == 0 and torch.cuda.is_available():
                        torch.cuda.empty_cache()

                except RuntimeError as e:
                    if "CUDA" in str(e) and "memory" in str(e).lower():
                        logging.error(f"CUDA内存不足，跳过批次 {batch_idx}: {e}")
                        if torch.cuda.is_available():
                            torch.cuda.empty_cache()
                        continue
                    else:
                        raise e

        return all_predictions, all_targets

    def compute_detailed_metrics(self, predictions: List[List[int]],
                               targets: List[List[int]]) -> Dict:
        """计算详细的评估指标"""
        results = {}

        for level in range(len(predictions)):
            pred = np.array(predictions[level])
            target = np.array(targets[level])

            # 基础指标
            accuracy = accuracy_score(target, pred)

            # 精确率、召回率、F1分数
            precision_macro, recall_macro, f1_macro, _ = precision_recall_fscore_support(
                target, pred, average='macro', zero_division=0
            )
            precision_micro, recall_micro, f1_micro, _ = precision_recall_fscore_support(
                target, pred, average='micro', zero_division=0
            )
            precision_weighted, recall_weighted, f1_weighted, _ = precision_recall_fscore_support(
                target, pred, average='weighted', zero_division=0
            )

            # 每个类别的详细指标
            precision_per_class, recall_per_class, f1_per_class, support_per_class = precision_recall_fscore_support(
                target, pred, average=None, zero_division=0
            )

            results[f'level_{level}'] = {
                'accuracy': accuracy,
                'macro_precision': precision_macro,
                'macro_recall': recall_macro,
                'macro_f1': f1_macro,
                'micro_precision': precision_micro,
                'micro_recall': recall_micro,
                'micro_f1': f1_micro,
                'weighted_precision': precision_weighted,
                'weighted_recall': recall_weighted,
                'weighted_f1': f1_weighted,
                'per_class_precision': precision_per_class.tolist() if hasattr(precision_per_class, 'tolist') else list(precision_per_class),
                'per_class_recall': recall_per_class.tolist() if hasattr(recall_per_class, 'tolist') else list(recall_per_class),
                'per_class_f1': f1_per_class.tolist() if hasattr(f1_per_class, 'tolist') else list(f1_per_class),
                'per_class_support': support_per_class.tolist() if hasattr(support_per_class, 'tolist') else list(support_per_class),
                'num_classes': len(np.unique(target))
            }

        # 计算整体指标
        overall_accuracy = np.mean([results[f'level_{i}']['accuracy'] for i in range(len(predictions))])
        overall_macro_f1 = np.mean([results[f'level_{i}']['macro_f1'] for i in range(len(predictions))])
        overall_micro_f1 = np.mean([results[f'level_{i}']['micro_f1'] for i in range(len(predictions))])
        overall_weighted_f1 = np.mean([results[f'level_{i}']['weighted_f1'] for i in range(len(predictions))])

        results['overall'] = {
            'accuracy': overall_accuracy,
            'macro_f1': overall_macro_f1,
            'micro_f1': overall_micro_f1,
            'weighted_f1': overall_weighted_f1
        }

        return results

    def generate_classification_reports(self, predictions: List[List[int]],
                                      targets: List[List[int]]) -> Dict[str, str]:
        """生成分类报告"""
        reports = {}

        for level in range(len(predictions)):
            pred = np.array(predictions[level])
            target = np.array(targets[level])

            # 获取实际出现的类别
            unique_labels = sorted(set(target.tolist() + pred.tolist()))

            # 获取标签名称，只包含实际出现的类别
            idx_to_label = self.label_mappings[level]['idx_to_label']
            target_names = [idx_to_label.get(i, f'Class_{i}') for i in unique_labels]

            report = classification_report(
                target, pred,
                labels=unique_labels,
                target_names=target_names,
                zero_division=0,
                output_dict=False
            )
            reports[f'level_{level}'] = report

        return reports

    def compute_confusion_matrices(self, predictions: List[List[int]],
                                  targets: List[List[int]]) -> Dict:
        """计算混淆矩阵（不绘制图片）"""
        confusion_matrices = {}

        for level in range(len(predictions)):
            pred = np.array(predictions[level])
            target = np.array(targets[level])

            # 计算混淆矩阵
            cm = confusion_matrix(target, pred)
            confusion_matrices[f'level_{level}'] = {
                'matrix': cm.tolist(),
                'shape': cm.shape
            }

        return confusion_matrices

    def analyze_hierarchical_consistency(self, predictions: List[List[int]],
                                       targets: List[List[int]]) -> Dict:
        """分析层次一致性 - 基于实际数据的层次关系"""
        consistency_results = {}

        # 构建层次映射关系（从训练数据中学习）
        hierarchy_mapping = self._build_hierarchy_mapping()

        for level in range(1, len(predictions)):
            parent_pred = np.array(predictions[level-1])
            child_pred = np.array(predictions[level])
            parent_target = np.array(targets[level-1])
            child_target = np.array(targets[level])

            # 计算预测一致性
            pred_consistency = self._compute_real_consistency_score(
                parent_pred, child_pred, level-1, level, hierarchy_mapping
            )

            # 计算目标一致性（应该是100%，因为数据本身是一致的）
            target_consistency = self._compute_real_consistency_score(
                parent_target, child_target, level-1, level, hierarchy_mapping
            )

            consistency_results[f'level_{level-1}_to_{level}'] = {
                'prediction_consistency': pred_consistency,
                'target_consistency': target_consistency,
                'total_samples': len(parent_pred)
            }

        return consistency_results

    def _build_hierarchy_mapping(self) -> Dict:
        """从训练数据构建层次映射关系"""
        hierarchy_mapping = {}

        # 从训练数据中学习层次关系
        try:
            import json
            with open('/data/TACL_chinese1_reset_v2/processed_data/train.json', 'r') as f:
                train_data = json.load(f)

            # 为每一层构建父子映射
            for level in range(1, len(self.num_classes_per_level)):
                parent_to_children = {}

                for sample in train_data:
                    labels = sample['labels']
                    if len(labels) > level:
                        parent_label = labels[level-1]
                        child_label = labels[level]

                        if parent_label not in parent_to_children:
                            parent_to_children[parent_label] = set()
                        parent_to_children[parent_label].add(child_label)

                # 转换为列表格式
                hierarchy_mapping[f'{level-1}_to_{level}'] = {
                    parent: list(children) for parent, children in parent_to_children.items()
                }

        except Exception as e:
            print(f"警告：无法构建层次映射，使用简化方法: {e}")
            # 如果无法读取训练数据，使用简化映射
            for level in range(1, len(self.num_classes_per_level)):
                hierarchy_mapping[f'{level-1}_to_{level}'] = {}

        return hierarchy_mapping

    def _compute_real_consistency_score(self, parent_labels: np.ndarray,
                                      child_labels: np.ndarray,
                                      parent_level: int, child_level: int,
                                      hierarchy_mapping: Dict) -> float:
        """计算真实的层次一致性分数"""
        mapping_key = f'{parent_level}_to_{child_level}'

        if mapping_key not in hierarchy_mapping:
            # 如果没有映射关系，返回0
            return 0.0

        parent_to_children = hierarchy_mapping[mapping_key]
        consistency_count = 0
        total_count = len(parent_labels)

        for i in range(total_count):
            parent_label = parent_labels[i]
            child_label = child_labels[i]

            # 检查子标签是否在父标签的合法子标签集合中
            if parent_label in parent_to_children:
                valid_children = parent_to_children[parent_label]
                if child_label in valid_children:
                    consistency_count += 1
            # 如果父标签没有对应的子标签映射，认为不一致

        return consistency_count / total_count if total_count > 0 else 0.0

    def full_evaluation(self, test_data_path: str) -> Dict:
        """完整评估"""
        print("开始模型评估...")

        # 创建数据加载器 - 使用更小的批处理大小以避免CUDA内存不足
        _, _, test_loader = create_data_loaders(
            'processed_data/train.json',  # 占位符
            'processed_data/val.json',    # 占位符
            test_data_path,
            self.tokenizer,
            batch_size=4,  # 减小批处理大小以避免CUDA内存不足
            max_length=512,
            num_workers=2,  # 减少worker数量
            use_balanced_sampling=False
        )

        # 评估
        predictions, targets = self.evaluate_on_dataset(test_loader)

        # 计算指标
        detailed_metrics = self.compute_detailed_metrics(predictions, targets)
        classification_reports = self.generate_classification_reports(predictions, targets)
        consistency_analysis = self.analyze_hierarchical_consistency(predictions, targets)

        # 计算混淆矩阵
        confusion_matrices = self.compute_confusion_matrices(predictions, targets)

        # 汇总结果
        evaluation_results = {
            'detailed_metrics': detailed_metrics,
            'classification_reports': classification_reports,
            'consistency_analysis': consistency_analysis,
            'confusion_matrices': confusion_matrices,
            'predictions': predictions,
            'targets': targets
        }

        return evaluation_results

def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='模型评估')
    parser.add_argument('--model_path', type=str, default='/data/TACL_chinese1_reset_v2/Qwen3-0.6B',
                       help='模型路径')
    parser.add_argument('--checkpoint', type=str, default='/data/TACL_chinese1_reset/checkpoints/ultra_level6_best_model.pth',
                       help='检查点路径')
    parser.add_argument('--test_data', type=str, default='/data/TACL_chinese1_reset_v2/processed_data/test.json',
                       help='测试数据路径')
    parser.add_argument('--output', type=str, default=f'{setup_evaluation_logging()}/evaluation_results.json',
                       help='输出文件路径')

    args = parser.parse_args()

    # 设置日志
    log_dir = setup_evaluation_logging()

    logging.info("🚀 开始模型评估")
    logging.info(f"📁 模型路径: {args.model_path}")
    logging.info(f"📁 检查点路径: {args.checkpoint}")
    logging.info(f"📁 测试数据路径: {args.test_data}")

    # 加载标签映射
    with open('/data/TACL_chinese1_reset_v2/processed_data/label_mappings.pkl', 'rb') as f:
        label_mappings = pickle.load(f)

    num_classes_per_level = [label_mappings[i]['num_classes'] for i in range(7)]

    # 创建评估器
    evaluator = ModelEvaluator(
        model_path=args.model_path,
        checkpoint_path=args.checkpoint,
        num_classes_per_level=num_classes_per_level,
        label_mappings=label_mappings
    )

    # 执行评估
    logging.info("📊 开始执行评估...")
    results = evaluator.full_evaluation(args.test_data)

    # 保存结果（排除predictions和targets以减少文件大小）
    save_results = {
        'detailed_metrics': results['detailed_metrics'],
        'classification_reports': results['classification_reports'],
        'consistency_analysis': results['consistency_analysis']
    }

    # 确定输出文件路径
    if args.output == 'evaluation_results.json':
        output_path = os.path.join(log_dir, 'evaluation_results.json')
    else:
        output_path = args.output

    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(save_results, f, ensure_ascii=False, indent=2)

    # 打印主要结果
    print("\n=== 评估结果 ===")
    overall = results['detailed_metrics']['overall']
    print(f"整体准确率: {overall['accuracy']:.4f}")
    print(f"整体Macro F1: {overall['macro_f1']:.4f}")
    print(f"整体Micro F1: {overall['micro_f1']:.4f}")
    print(f"整体Weighted F1: {overall['weighted_f1']:.4f}")

    print("\n各层级详细结果:")
    for level in range(len(num_classes_per_level)):
        level_results = results['detailed_metrics'][f'level_{level}']
        print(f"Level {level}:")
        print(f"  准确率: {level_results['accuracy']:.4f}")
        print(f"  Macro F1: {level_results['macro_f1']:.4f}")
        print(f"  Micro F1: {level_results['micro_f1']:.4f}")
        print(f"  类别数: {level_results['num_classes']}")

    print(f"\n详细结果已保存到: {output_path}")

if __name__ == "__main__":
    main()