#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级训练脚本 - 使用多种先进技术提升层次化分类效果
"""

import sys
import os
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import pickle
import json
import argparse
import logging
from transformers import AutoTokenizer
from transformers.optimization import get_linear_schedule_with_warmup, get_cosine_schedule_with_warmup
from tqdm import tqdm
from torch.utils.tensorboard import SummaryWriter
from datetime import datetime
import random
from collections import defaultdict, Counter
from sklearn.utils.class_weight import compute_class_weight

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from src.models.hierarchical_model import HierarchicalQwenModel
from src.data.data_loader import create_data_loaders

class Level6FocusedLoss(nn.Module):
    """专门针对Level 6优化的层次化损失函数"""

    def __init__(self, num_classes_per_level, hierarchy_weight=0.3, level_weights=None,
                 level6_boost=3.0, use_focal_for_level6=True):
        super().__init__()
        self.num_classes_per_level = num_classes_per_level
        self.hierarchy_weight = hierarchy_weight
        self.level6_boost = level6_boost  # Level 6的权重提升倍数
        self.use_focal_for_level6 = use_focal_for_level6

        # 设置层级权重，Level 6获得最高权重
        if level_weights is None:
            level_weights = [1.0] * len(num_classes_per_level)
            if len(level_weights) > 6:
                level_weights[6] = level6_boost  # Level 6权重提升
        self.level_weights = level_weights

        # 构建层次映射关系
        self.hierarchy_mapping = self._build_hierarchy_mapping()

        # 为Level 6创建Focal Loss
        if self.use_focal_for_level6:
            self.focal_loss = FocalLoss(alpha=1.0, gamma=2.0)

        # 计算Level 6的类别权重
        self.level6_class_weights = self._compute_level6_class_weights()

    def _compute_level6_class_weights(self):
        """计算Level 6的类别权重"""
        try:
            with open('/data/TACL_chinese1_reset_v2/processed_data/train.json', 'r') as f:
                train_data = json.load(f)

            # 统计Level 6的类别分布
            level6_labels = [sample['labels'][6] for sample in train_data if len(sample['labels']) > 6]

            if len(level6_labels) > 0:
                from sklearn.utils.class_weight import compute_class_weight
                unique_labels = list(set(level6_labels))
                class_weights = compute_class_weight(
                    'balanced',
                    classes=np.array(unique_labels),
                    y=np.array(level6_labels)
                )
                weight_dict = dict(zip(unique_labels, class_weights))

                # 创建完整的权重张量
                weights = torch.ones(self.num_classes_per_level[6])
                for label, weight in weight_dict.items():
                    if label < len(weights):
                        weights[label] = weight

                return weights
            else:
                return torch.ones(self.num_classes_per_level[6])
        except Exception as e:
            print(f"警告：无法计算Level 6类别权重: {e}")
            return torch.ones(self.num_classes_per_level[6])
        
    def _build_hierarchy_mapping(self):
        """从训练数据构建层次映射关系"""
        hierarchy_mapping = {}
        try:
            with open('/data/TACL_chinese1_reset_v2/processed_data/train.json', 'r') as f:
                train_data = json.load(f)
            
            for level in range(1, len(self.num_classes_per_level)):
                parent_to_children = defaultdict(set)
                for sample in train_data:
                    labels = sample['labels']
                    if len(labels) > level:
                        parent_label = labels[level-1]
                        child_label = labels[level]
                        parent_to_children[parent_label].add(child_label)
                
                hierarchy_mapping[f'{level-1}_to_{level}'] = {
                    parent: list(children) for parent, children in parent_to_children.items()
                }
        except Exception as e:
            print(f"警告：无法构建层次映射: {e}")
            
        return hierarchy_mapping
    
    def forward(self, logits, targets):
        """
        计算层次化损失 - 专门优化Level 6
        logits: List[Tensor] - 每层的logits
        targets: List[Tensor] - 每层的目标标签
        """
        total_loss = 0.0
        classification_losses = []

        # 1. 标准分类损失
        for level, (level_logits, level_targets) in enumerate(zip(logits, targets)):
            if level == 6 and self.use_focal_for_level6:
                # Level 6使用Focal Loss + 类别权重
                device = level_logits.device
                class_weights = self.level6_class_weights.to(device)

                # 使用加权的Focal Loss
                ce_loss = F.cross_entropy(level_logits, level_targets,
                                        weight=class_weights, reduction='none')
                pt = torch.exp(-ce_loss)
                focal_loss = (1 - pt) ** 2.0 * ce_loss  # gamma=2.0
                level_loss = focal_loss.mean()
            else:
                # 其他层级使用标准交叉熵
                level_loss = F.cross_entropy(level_logits, level_targets, reduction='mean')

            weighted_loss = level_loss * self.level_weights[level]
            classification_losses.append(weighted_loss)
            total_loss += weighted_loss

        # 2. 层次一致性损失
        hierarchy_loss = self._compute_hierarchy_loss(logits, targets)
        total_loss += self.hierarchy_weight * hierarchy_loss

        # 3. Level 6专门的正则化损失
        if len(logits) > 6:
            level6_reg_loss = self._compute_level6_regularization(logits[6], targets[6])
            total_loss += 0.1 * level6_reg_loss

        return {
            'total_loss': total_loss,
            'classification_losses': classification_losses,
            'hierarchy_loss': hierarchy_loss
        }

    def _compute_level6_regularization(self, level6_logits, level6_targets):
        """Level 6专门的正则化损失"""
        # 鼓励预测分布的多样性，避免过度集中在少数类别
        probs = F.softmax(level6_logits, dim=-1)
        entropy = -torch.sum(probs * torch.log(probs + 1e-8), dim=-1)

        # 鼓励适度的熵值（不要太低也不要太高）
        target_entropy = np.log(self.num_classes_per_level[6]) * 0.3  # 目标熵值
        entropy_loss = F.mse_loss(entropy, torch.full_like(entropy, target_entropy))

        return entropy_loss
    
    def _compute_hierarchy_loss(self, logits, targets):
        """计算层次一致性损失"""
        hierarchy_loss = 0.0
        
        for level in range(1, len(logits)):
            parent_logits = logits[level-1]
            child_logits = logits[level]
            parent_targets = targets[level-1]
            
            # 获取父类预测概率
            parent_probs = F.softmax(parent_logits, dim=-1)
            child_probs = F.softmax(child_logits, dim=-1)
            
            # 计算层次一致性约束
            mapping_key = f'{level-1}_to_{level}'
            if mapping_key in self.hierarchy_mapping:
                consistency_loss = self._consistency_constraint(
                    parent_probs, child_probs, parent_targets, 
                    self.hierarchy_mapping[mapping_key]
                )
                hierarchy_loss += consistency_loss
        
        return hierarchy_loss
    
    def _consistency_constraint(self, parent_probs, child_probs, parent_targets, mapping):
        """层次一致性约束"""
        batch_size = parent_probs.size(0)
        consistency_loss = 0.0
        
        for i in range(batch_size):
            parent_class = parent_targets[i].item()
            if parent_class in mapping:
                valid_children = mapping[parent_class]
                # 只有合法的子类应该有高概率
                valid_child_probs = child_probs[i, valid_children].sum()
                # 鼓励合法子类的概率和接近1
                consistency_loss += (1.0 - valid_child_probs) ** 2
        
        return consistency_loss / batch_size if batch_size > 0 else 0.0

class FocalLoss(nn.Module):
    """Focal Loss - 处理类别不平衡"""
    
    def __init__(self, alpha=1.0, gamma=2.0, reduction='mean'):
        super().__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.reduction = reduction
    
    def forward(self, inputs, targets):
        ce_loss = F.cross_entropy(inputs, targets, reduction='none')
        pt = torch.exp(-ce_loss)
        focal_loss = self.alpha * (1 - pt) ** self.gamma * ce_loss
        
        if self.reduction == 'mean':
            return focal_loss.mean()
        elif self.reduction == 'sum':
            return focal_loss.sum()
        else:
            return focal_loss

class AdvancedTrainer:
    """高级训练器"""
    
    def __init__(self, args):
        self.args = args
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

        # 设置随机种子
        self.set_seed(args.seed)

        # 创建日志
        self.setup_logging()

        # 加载数据和模型
        self.setup_data()
        self.setup_model()
        self.setup_training()
        
    def set_seed(self, seed):
        """设置随机种子"""
        random.seed(seed)
        np.random.seed(seed)
        torch.manual_seed(seed)
        if torch.cuda.is_available():
            torch.cuda.manual_seed_all(seed)
    
    def setup_data(self):
        """设置数据加载器"""
        logging.info("📚 设置数据加载器...")

        # 加载标签映射
        with open('/data/TACL_chinese1_reset_v2/processed_data/label_mappings.pkl', 'rb') as f:
            self.label_mappings = pickle.load(f)

        self.num_classes_per_level = [self.label_mappings[i]['num_classes'] for i in range(7)]

        # 创建tokenizer
        self.tokenizer = AutoTokenizer.from_pretrained(self.args.model_path)
        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token

        # 创建数据加载器 - 启用Level 6专门优化
        self.train_loader, self.val_loader, _ = create_data_loaders(
            'processed_data/train.json',
            'processed_data/val.json',
            'processed_data/test.json',
            self.tokenizer,
            batch_size=self.args.batch_size,
            max_length=self.args.max_length,
            num_workers=self.args.num_workers,
            use_balanced_sampling=self.args.use_balanced_sampling,
            use_level6_focus=getattr(self.args, 'use_level6_focus', True)
        )

        logging.info(f"✅ 数据加载完成")
        logging.info(f"🏷️  各层级类别数: {self.num_classes_per_level}")
        logging.info(f"📊 训练批次数: {len(self.train_loader)}")
        logging.info(f"📊 验证批次数: {len(self.val_loader)}")
        logging.info(f"📊 批次大小: {self.args.batch_size}")
        logging.info(f"📊 最大序列长度: {self.args.max_length}")
    
    def setup_model(self):
        """设置模型"""
        logging.info("🧠 创建模型...")

        self.model = HierarchicalQwenModel(
            model_path=self.args.model_path,
            num_classes_per_level=self.num_classes_per_level,
            max_length=self.args.max_length
        ).to(self.device)

        total_params = sum(p.numel() for p in self.model.parameters())
        trainable_params = sum(p.numel() for p in self.model.parameters() if p.requires_grad)

        logging.info(f"📊 模型总参数数量: {total_params:,}")
        logging.info(f"📊 可训练参数数量: {trainable_params:,}")
        logging.info(f"📊 模型大小: {total_params * 4 / 1024 / 1024:.2f} MB")
        logging.info(f"🖥️  设备: {self.device}")
    
    def setup_training(self):
        """设置训练组件"""
        print("🎯 设置训练组件...")
        
        # 计算类别权重（处理不平衡）
        if self.args.use_class_weights:
            self.class_weights = self.compute_class_weights()
        else:
            self.class_weights = None
        
        # 损失函数 - 使用Level 6专门优化的损失函数
        if self.args.loss_type == 'hierarchical':
            self.criterion = Level6FocusedLoss(
                self.num_classes_per_level,
                hierarchy_weight=self.args.hierarchy_weight,
                level6_boost=getattr(self.args, 'level6_boost', 3.0)
            )
        elif self.args.loss_type == 'focal':
            self.criterion = FocalLoss(gamma=self.args.focal_gamma)
        else:
            self.criterion = nn.CrossEntropyLoss()
        
        # 优化器 - 分层学习率
        backbone_params = []
        classifier_params = []
        
        for name, param in self.model.named_parameters():
            if 'classifier' in name:
                classifier_params.append(param)
            else:
                backbone_params.append(param)
        
        self.optimizer = torch.optim.AdamW([
            {'params': backbone_params, 'lr': self.args.backbone_lr},
            {'params': classifier_params, 'lr': self.args.classifier_lr}
        ], weight_decay=self.args.weight_decay)
        
        # 学习率调度器
        total_steps = len(self.train_loader) * self.args.epochs
        warmup_steps = int(total_steps * self.args.warmup_ratio)

        # 使用余弦退火调度器，更平滑的学习率衰减
        if hasattr(self.args, 'use_cosine_schedule') and self.args.use_cosine_schedule:
            self.scheduler = get_cosine_schedule_with_warmup(
                self.optimizer,
                num_warmup_steps=warmup_steps,
                num_training_steps=total_steps
            )
        else:
            self.scheduler = get_linear_schedule_with_warmup(
                self.optimizer,
                num_warmup_steps=warmup_steps,
                num_training_steps=total_steps
            )
        
        # 混合精度训练
        if self.args.use_amp:
            self.scaler = torch.cuda.amp.GradScaler()
        else:
            self.scaler = None
    
    def compute_class_weights(self):
        """计算类别权重"""
        print("⚖️  计算类别权重...")
        
        # 统计每层的类别分布
        with open('/data/TACL_chinese1_reset_v2/processed_data/train.json', 'r') as f:
            train_data = json.load(f)
        
        class_weights = []
        for level in range(len(self.num_classes_per_level)):
            level_labels = [sample['labels'][level] for sample in train_data if len(sample['labels']) > level]
            
            if len(set(level_labels)) > 1:  # 多于1个类别才计算权重
                weights = compute_class_weight(
                    'balanced',
                    classes=np.unique(level_labels),
                    y=level_labels
                )
                weight_dict = dict(zip(np.unique(level_labels), weights))
                # 创建完整的权重张量
                level_weights = torch.ones(self.num_classes_per_level[level])
                for class_idx, weight in weight_dict.items():
                    level_weights[class_idx] = weight
                class_weights.append(level_weights.to(self.device))
            else:
                class_weights.append(torch.ones(self.num_classes_per_level[level]).to(self.device))
        
        return class_weights
    
    def setup_logging(self):
        """设置日志"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # 创建日志目录
        log_dir = f"/data/TACL_chinese1_reset_v2/results/training/advanced_train_{timestamp}"
        os.makedirs(log_dir, exist_ok=True)

        # 设置文件日志
        log_file = os.path.join(log_dir, "training.log")
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )

        # TensorBoard日志
        tensorboard_dir = os.path.join(log_dir, "tensorboard")
        self.writer = SummaryWriter(log_dir=tensorboard_dir)

        # 保存配置
        config_file = os.path.join(log_dir, "config.json")
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(vars(self.args), f, indent=2, ensure_ascii=False)

        logging.info(f"📊 训练日志目录: {log_dir}")
        logging.info(f"📊 TensorBoard日志: {tensorboard_dir}")
        logging.info(f"⚙️  配置文件: {config_file}")

        self.log_dir = log_dir
    
    def get_active_levels(self, epoch):
        """课程学习：确定当前epoch应该训练的层级 - Level 6专门优化版"""
        if hasattr(self.args, 'curriculum_learning') and self.args.curriculum_learning:
            # 渐进式训练策略，专门为Level 6设计
            if epoch < 2:
                return [0, 1]  # 只训练前2层，建立基础
            elif epoch < 4:
                return [0, 1, 2]  # 训练前3层
            elif epoch < 6:
                return [0, 1, 2, 3]  # 训练前4层
            elif epoch < 8:
                return [0, 1, 2, 3, 4]  # 训练前5层
            elif epoch < 10:
                return [0, 1, 2, 3, 4, 5]  # 训练前6层
            elif epoch < 15:
                # 开始引入Level 6，但权重较低
                return list(range(len(self.num_classes_per_level)))
            else:
                # 全力训练所有层级，特别关注Level 6
                return list(range(len(self.num_classes_per_level)))
        else:
            return list(range(len(self.num_classes_per_level)))  # 训练所有层

    def get_level6_focus_weight(self, epoch):
        """获取Level 6的动态权重"""
        if epoch < 10:
            return 1.0  # 早期阶段正常权重
        elif epoch < 20:
            return 2.0  # 中期阶段提升权重
        else:
            return 3.0  # 后期阶段最高权重

    def train_epoch(self, epoch):
        """训练一个epoch"""
        self.model.train()
        total_loss = 0.0
        level_losses = [0.0] * len(self.num_classes_per_level)

        # 获取当前应该训练的层级
        active_levels = self.get_active_levels(epoch)

        progress_bar = tqdm(self.train_loader, desc=f"Epoch {epoch+1}/{self.args.epochs} (Levels: {active_levels})")

        for batch_idx, batch in enumerate(progress_bar):
            input_ids = batch['input_ids'].to(self.device)
            attention_mask = batch['attention_mask'].to(self.device)
            labels = batch['labels'].to(self.device)
            
            # 转换标签格式
            labels_list = [labels[:, i] for i in range(labels.size(1))]
            
            self.optimizer.zero_grad()
            
            if self.scaler:
                with torch.cuda.amp.autocast():
                    outputs = self.model(input_ids, attention_mask, labels_list)
                    # 只计算活跃层级的损失
                    active_logits = [outputs['logits'][i] for i in active_levels]
                    active_labels = [labels_list[i] for i in active_levels]
                    loss_dict = self.criterion(active_logits, active_labels)
                    loss = loss_dict['total_loss']
                
                self.scaler.scale(loss).backward()
                self.scaler.unscale_(self.optimizer)
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), self.args.grad_clip)
                self.scaler.step(self.optimizer)
                self.scaler.update()
            else:
                outputs = self.model(input_ids, attention_mask, labels_list)
                # 只计算活跃层级的损失
                active_logits = [outputs['logits'][i] for i in active_levels]
                active_labels = [labels_list[i] for i in active_levels]
                loss_dict = self.criterion(active_logits, active_labels)
                loss = loss_dict['total_loss']
                
                loss.backward()
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), self.args.grad_clip)
                self.optimizer.step()
            
            self.scheduler.step()
            
            total_loss += loss.item()
            
            # 更新进度条
            progress_bar.set_postfix({
                'Loss': f'{loss.item():.4f}',
                'LR': f'{self.scheduler.get_last_lr()[0]:.2e}'
            })
            
            # 记录到TensorBoard
            global_step = epoch * len(self.train_loader) + batch_idx
            self.writer.add_scalar('Train/Loss', loss.item(), global_step)
            self.writer.add_scalar('Train/LR', self.scheduler.get_last_lr()[0], global_step)
        
        return total_loss / len(self.train_loader)
    
    def validate(self):
        """验证"""
        self.model.eval()
        total_loss = 0.0
        correct_predictions = [0] * len(self.num_classes_per_level)
        total_predictions = 0
        
        with torch.no_grad():
            for batch in tqdm(self.val_loader, desc="Validating"):
                input_ids = batch['input_ids'].to(self.device)
                attention_mask = batch['attention_mask'].to(self.device)
                labels = batch['labels'].to(self.device)
                
                labels_list = [labels[:, i] for i in range(labels.size(1))]
                
                outputs = self.model(input_ids, attention_mask, labels_list)
                loss_dict = self.criterion(outputs['logits'], labels_list)
                loss = loss_dict['total_loss']
                
                total_loss += loss.item()
                
                # 计算准确率
                for level, logits in enumerate(outputs['logits']):
                    predictions = torch.argmax(logits, dim=-1)
                    correct_predictions[level] += (predictions == labels_list[level]).sum().item()
                
                total_predictions += labels.size(0)
        
        avg_loss = total_loss / len(self.val_loader)
        accuracies = [correct / total_predictions for correct in correct_predictions]
        
        return avg_loss, accuracies
    
    def train(self):
        """完整训练流程"""
        logging.info("🚀 开始高级训练...")
        logging.info(f"📱 设备: {self.device}")
        logging.info(f"🎯 训练策略: {self.args.loss_type} loss")
        logging.info(f"⚖️  类别权重: {'启用' if self.args.use_class_weights else '禁用'}")
        logging.info(f"🔥 混合精度: {'启用' if self.args.use_amp else '禁用'}")
        logging.info(f"📚 课程学习: {'启用' if self.args.curriculum_learning else '禁用'}")
        logging.info(f"🎓 分层学习率: backbone={self.args.backbone_lr}, classifier={self.args.classifier_lr}")

        best_val_loss = float('inf')
        patience_counter = 0
        training_history = []

        for epoch in range(self.args.epochs):
            epoch_start_time = datetime.now()

            # 训练
            train_loss = self.train_epoch(epoch)

            # 验证
            val_loss, val_accuracies = self.validate()

            epoch_end_time = datetime.now()
            epoch_duration = (epoch_end_time - epoch_start_time).total_seconds()

            # 记录指标
            self.writer.add_scalar('Epoch/TrainLoss', train_loss, epoch)
            self.writer.add_scalar('Epoch/ValLoss', val_loss, epoch)
            for level, acc in enumerate(val_accuracies):
                self.writer.add_scalar(f'Epoch/ValAcc_Level_{level}', acc, epoch)

            # 记录训练历史
            epoch_info = {
                'epoch': epoch + 1,
                'train_loss': train_loss,
                'val_loss': val_loss,
                'val_accuracies': val_accuracies,
                'duration': epoch_duration,
                'timestamp': epoch_end_time.isoformat()
            }
            training_history.append(epoch_info)

            logging.info(f"\n{'='*60}")
            logging.info(f"Epoch {epoch+1}/{self.args.epochs} 完成 (耗时: {epoch_duration:.1f}s)")
            logging.info(f"训练损失: {train_loss:.4f}")
            logging.info(f"验证损失: {val_loss:.4f}")
            logging.info(f"验证准确率:")
            for level, acc in enumerate(val_accuracies):
                logging.info(f"Level {level}: {acc:.4f}")

            # 保存最佳模型
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                patience_counter = 0

                checkpoint = {
                    'epoch': epoch,
                    'model_state_dict': self.model.state_dict(),
                    'optimizer_state_dict': self.optimizer.state_dict(),
                    'val_loss': val_loss,
                    'val_accuracies': val_accuracies,
                    'args': vars(self.args),
                    'training_history': training_history
                }


                os.makedirs('/data/TACL_chinese1_reset_v2/checkpoints', exist_ok=True)
                checkpoint_path = '/data/TACL_chinese1_reset_v2/checkpoints/advanced_best_model.pth'
                torch.save(checkpoint, checkpoint_path)
                logging.info(f"💾 保存最佳模型: {checkpoint_path} (验证损失: {val_loss:.4f})")
            else:
                patience_counter += 1
                logging.info(f"⏳ 验证损失未改善 ({patience_counter}/{self.args.patience})")
                if patience_counter >= self.args.patience:
                    logging.info(f"⏹️  早停：验证损失连续{self.args.patience}个epoch未改善")
                    break

        # 保存训练历史
        history_file = os.path.join(self.log_dir, "training_history.json")
        with open(history_file, 'w', encoding='utf-8') as f:
            json.dump(training_history, f, indent=2, ensure_ascii=False)

        self.writer.close()
        logging.info("🎉 高级训练完成!")
        logging.info(f"📊 训练历史已保存: {history_file}")
        return best_val_loss

def parse_args():
    parser = argparse.ArgumentParser(description='高级层次化分类训练')
    
    # 基本参数
    parser.add_argument('--model_path', type=str, default='/data/TACL_chinese1_reset_v2/Qwen3-0.6B')
    parser.add_argument('--max_length', type=int, default=512)
    parser.add_argument('--batch_size', type=int, default=8)
    parser.add_argument('--epochs', type=int, default=35)  # 减少epochs
    parser.add_argument('--seed', type=int, default=42)

    # 学习率参数 - 降低学习率提升稳定性
    parser.add_argument('--backbone_lr', type=float, default=1e-7)  # 降低
    parser.add_argument('--classifier_lr', type=float, default=1e-5)  # 降低
    parser.add_argument('--weight_decay', type=float, default=0.01)  # 增加正则化
    parser.add_argument('--warmup_ratio', type=float, default=0.4)  # 增加预热
    
    # 训练策略
    parser.add_argument('--loss_type', type=str, default='hierarchical',
                       choices=['ce', 'focal', 'hierarchical'])
    parser.add_argument('--hierarchy_weight', type=float, default=1.2)  # 降低层次损失权重
    parser.add_argument('--focal_gamma', type=float, default=2.0)
    parser.add_argument('--use_class_weights', action='store_true')
    parser.add_argument('--use_balanced_sampling', action='store_true')
    parser.add_argument('--use_amp', action='store_true')
    parser.add_argument('--curriculum_learning', action='store_true')
    parser.add_argument('--use_cosine_schedule', action='store_true', help='使用余弦退火学习率调度')

    # Level 6专门优化参数
    parser.add_argument('--use_level6_focus', action='store_true', default=True, help='启用Level 6专门优化')
    parser.add_argument('--level6_boost', type=float, default=3.0, help='Level 6损失权重提升倍数')

    # 其他参数
    parser.add_argument('--grad_clip', type=float, default=0.1)  # 更严格的梯度裁剪
    parser.add_argument('--patience', type=int, default=8)  # 增加patience
    parser.add_argument('--num_workers', type=int, default=2)
    
    return parser.parse_args()

def main():
    args = parse_args()
    trainer = AdvancedTrainer(args)
    trainer.train()

if __name__ == "__main__":
    main()



'''
CUDA_VISIBLE_DEVICES=1 /home/<USER>/anaconda3/envs/htc/bin/python advanced_train.py \
    --loss_type hierarchical \
    --use_class_weights \
    --curriculum_learning \
    --use_amp \
    --use_cosine_schedule
'''