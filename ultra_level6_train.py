#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
超激进Level 6优化训练脚本
采用极端策略将Level 6准确率从15.42%提升到90%以上
"""

import sys
import os
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import pickle
import json
import argparse
import logging
from transformers import AutoTokenizer
from transformers.optimization import get_cosine_schedule_with_warmup
from tqdm import tqdm
from torch.utils.tensorboard import SummaryWriter
from datetime import datetime
import random
from collections import defaultdict, Counter

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from src.models.hierarchical_model import HierarchicalQwenModel
from src.data.data_loader import create_data_loaders
from advanced_train import Level6FocusedLoss, FocalLoss

class UltraLevel6Trainer:
    """超激进Level 6优化训练器"""
    
    def __init__(self, args):
        self.args = args
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 设置随机种子
        self.set_seed(args.seed)
        
        # 创建日志
        self.setup_logging()
        
        # 加载数据和模型
        self.setup_data()
        self.setup_model()
        self.setup_training()
    
    def set_seed(self, seed):
        """设置随机种子"""
        random.seed(seed)
        np.random.seed(seed)
        torch.manual_seed(seed)
        if torch.cuda.is_available():
            torch.cuda.manual_seed_all(seed)
    
    def setup_data(self):
        """设置数据加载器"""
        logging.info("📚 设置超激进Level 6优化的数据加载器...")
        
        # 加载标签映射
        with open('/data/TACL_chinese1_reset_v2/processed_data/label_mappings.pkl', 'rb') as f:
            self.label_mappings = pickle.load(f)
        
        self.num_classes_per_level = [self.label_mappings[i]['num_classes'] for i in range(7)]
        
        # 创建tokenizer
        self.tokenizer = AutoTokenizer.from_pretrained(self.args.model_path)
        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token
        
        # 创建数据加载器 - 启用所有Level 6优化
        self.train_loader, self.val_loader, _ = create_data_loaders(
            'processed_data/train.json',
            'processed_data/val.json',
            'processed_data/test.json',
            self.tokenizer,
            batch_size=self.args.batch_size,
            max_length=self.args.max_length,
            num_workers=self.args.num_workers,
            use_balanced_sampling=True,
            use_level6_focus=True  # 启用Level 6专门优化
        )
        
        logging.info(f"✅ 超激进Level 6优化数据加载完成")
        logging.info(f"🏷️  各层级类别数: {self.num_classes_per_level}")
    
    def setup_model(self):
        """设置模型"""
        logging.info("🧠 创建超激进Level 6优化模型...")
        
        self.model = HierarchicalQwenModel(
            model_path=self.args.model_path,
            num_classes_per_level=self.num_classes_per_level,
            max_length=self.args.max_length,
            dropout=0.05  # 更低的dropout
        ).to(self.device)
        
        # 冻结前面层级的参数，专注训练Level 6
        if self.args.freeze_early_levels:
            for name, param in self.model.named_parameters():
                # 只训练Level 6相关的参数
                if not any(keyword in name.lower() for keyword in ['level6', 'classifier.6', 'hierarchical_classifier']):
                    param.requires_grad = False
        
        trainable_params = sum(p.numel() for p in self.model.parameters() if p.requires_grad)
        logging.info(f"📊 可训练参数数量: {trainable_params:,}")
    
    def setup_training(self):
        """设置训练组件"""
        logging.info("🎯 设置超激进Level 6专门优化的训练组件...")
        
        # 使用极端的Level 6专门优化损失函数
        self.criterion = Level6FocusedLoss(
            self.num_classes_per_level,
            hierarchy_weight=0.1,      # 大幅降低层次损失权重
            level6_boost=10.0,         # Level 6权重极大提升
            use_focal_for_level6=True
        )
        
        # 只优化Level 6相关参数
        level6_params = []
        other_params = []
        
        for name, param in self.model.named_parameters():
            if param.requires_grad:
                if any(keyword in name.lower() for keyword in ['level6', 'classifier.6']):
                    level6_params.append(param)
                else:
                    other_params.append(param)
        
        # 使用更激进的学习率
        param_groups = []
        if len(level6_params) > 0:
            param_groups.append({'params': level6_params, 'lr': self.args.level6_lr})
        if len(other_params) > 0:
            param_groups.append({'params': other_params, 'lr': self.args.other_lr})
        
        self.optimizer = torch.optim.AdamW(param_groups, weight_decay=0.001)  # 更低的权重衰减
        
        # 学习率调度器
        total_steps = len(self.train_loader) * self.args.epochs
        warmup_steps = int(total_steps * 0.05)  # 5%预热
        
        self.scheduler = get_cosine_schedule_with_warmup(
            self.optimizer,
            num_warmup_steps=warmup_steps,
            num_training_steps=total_steps
        )
        
        # 混合精度训练
        if self.args.use_amp:
            self.scaler = torch.cuda.amp.GradScaler()
        else:
            self.scaler = None
        
        logging.info(f"✅ 超激进Level 6专门优化训练设置完成")
        logging.info(f"🎯 Level 6参数数量: {sum(p.numel() for p in level6_params)}")
    
    def setup_logging(self):
        """设置日志"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 创建日志目录
        log_dir = f"/data/TACL_chinese1_reset_v2/results/training/ultra_level6_{timestamp}"
        os.makedirs(log_dir, exist_ok=True)
        
        # 设置文件日志
        log_file = os.path.join(log_dir, "training.log")
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        # TensorBoard日志
        tensorboard_dir = os.path.join(log_dir, "tensorboard")
        self.writer = SummaryWriter(log_dir=tensorboard_dir)
        
        logging.info(f"📊 超激进Level 6优化训练日志目录: {log_dir}")
        self.log_dir = log_dir
    
    def ultra_level6_training_strategy(self, epoch):
        """超激进Level 6训练策略"""
        # 只训练Level 6，忽略其他层级
        return [6]  # 只训练Level 6
    
    def train_epoch(self, epoch):
        """训练一个epoch - 专注Level 6"""
        self.model.train()
        total_loss = 0.0
        level6_correct = 0
        level6_total = 0
        
        # 获取当前应该训练的层级 - 只训练Level 6
        active_levels = self.ultra_level6_training_strategy(epoch)
        
        progress_bar = tqdm(self.train_loader, 
                           desc=f"Epoch {epoch+1}/{self.args.epochs} (ULTRA Level 6 ONLY)")
        
        for batch_idx, batch in enumerate(progress_bar):
            input_ids = batch['input_ids'].to(self.device)
            attention_mask = batch['attention_mask'].to(self.device)
            labels = batch['labels'].to(self.device)
            
            # 转换标签格式
            labels_list = [labels[:, i] for i in range(labels.size(1))]
            
            self.optimizer.zero_grad()
            
            if self.scaler:
                with torch.cuda.amp.autocast():
                    outputs = self.model(input_ids, attention_mask, labels_list)
                    # 只计算Level 6的损失
                    level6_logits = outputs['logits'][6]
                    level6_labels = labels_list[6]
                    
                    # 使用极端的Level 6专门损失
                    loss = F.cross_entropy(level6_logits, level6_labels) * 10.0  # 10倍权重
                
                self.scaler.scale(loss).backward()
                self.scaler.unscale_(self.optimizer)
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), 0.5)
                self.scaler.step(self.optimizer)
                self.scaler.update()
            else:
                outputs = self.model(input_ids, attention_mask, labels_list)
                level6_logits = outputs['logits'][6]
                level6_labels = labels_list[6]
                
                # 使用极端的Level 6专门损失
                loss = F.cross_entropy(level6_logits, level6_labels) * 10.0  # 10倍权重
                
                loss.backward()
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), 0.5)
                self.optimizer.step()
            
            self.scheduler.step()
            total_loss += loss.item()
            
            # 计算Level 6准确率
            level6_pred = torch.argmax(level6_logits, dim=-1)
            level6_correct += (level6_pred == level6_labels).sum().item()
            level6_total += labels.size(0)
            
            # 更新进度条
            level6_acc = level6_correct / level6_total if level6_total > 0 else 0
            progress_bar.set_postfix({
                'Loss': f'{loss.item():.4f}',
                'Level6_Acc': f'{level6_acc:.4f}',
                'LR': f'{self.scheduler.get_last_lr()[0]:.2e}'
            })
            
            # 记录到TensorBoard
            global_step = epoch * len(self.train_loader) + batch_idx
            self.writer.add_scalar('Train/Loss', loss.item(), global_step)
            self.writer.add_scalar('Train/Level6_Acc', level6_acc, global_step)
        
        return total_loss / len(self.train_loader), level6_correct / level6_total if level6_total > 0 else 0
    
    def validate(self):
        """验证 - 只关注Level 6"""
        self.model.eval()
        level6_correct = 0
        level6_total = 0
        
        with torch.no_grad():
            for batch in tqdm(self.val_loader, desc="Validating (ULTRA Level 6)"):
                input_ids = batch['input_ids'].to(self.device)
                attention_mask = batch['attention_mask'].to(self.device)
                labels = batch['labels'].to(self.device)
                
                labels_list = [labels[:, i] for i in range(labels.size(1))]
                
                outputs = self.model(input_ids, attention_mask, labels_list)
                
                # 只计算Level 6准确率
                level6_logits = outputs['logits'][6]
                level6_labels = labels_list[6]
                level6_pred = torch.argmax(level6_logits, dim=-1)
                level6_correct += (level6_pred == level6_labels).sum().item()
                level6_total += labels.size(0)
        
        level6_accuracy = level6_correct / level6_total if level6_total > 0 else 0
        return level6_accuracy
    
    def train(self):
        """完整训练流程 - 超激进Level 6专门优化"""
        logging.info("🚀 开始超激进Level 6专门优化训练...")
        logging.info(f"🎯 目标：Level 6准确率从15.42%提升到90%以上")
        logging.info(f"⚡ 策略：只训练Level 6，忽略其他层级")
        
        best_level6_acc = 0.0
        patience_counter = 0
        
        for epoch in range(self.args.epochs):
            epoch_start_time = datetime.now()
            
            # 训练
            train_loss, train_level6_acc = self.train_epoch(epoch)
            
            # 验证
            val_level6_acc = self.validate()
            
            epoch_end_time = datetime.now()
            epoch_duration = (epoch_end_time - epoch_start_time).total_seconds()
            
            # 记录指标
            self.writer.add_scalar('Epoch/TrainLoss', train_loss, epoch)
            self.writer.add_scalar('Epoch/TrainLevel6Acc', train_level6_acc, epoch)
            self.writer.add_scalar('Epoch/ValLevel6Acc', val_level6_acc, epoch)
            
            logging.info(f"\n{'='*80}")
            logging.info(f"Epoch {epoch+1}/{self.args.epochs} 完成 (耗时: {epoch_duration:.1f}s)")
            logging.info(f"训练损失: {train_loss:.4f}")
            logging.info(f"🎯 Level 6训练准确率: {train_level6_acc:.4f} ({train_level6_acc*100:.2f}%)")
            logging.info(f"🎯 Level 6验证准确率: {val_level6_acc:.4f} ({val_level6_acc*100:.2f}%)")
            
            # 保存最佳Level 6模型
            if val_level6_acc > best_level6_acc:
                best_level6_acc = val_level6_acc
                patience_counter = 0
                
                checkpoint = {
                    'epoch': epoch,
                    'model_state_dict': self.model.state_dict(),
                    'optimizer_state_dict': self.optimizer.state_dict(),
                    'val_level6_acc': val_level6_acc,
                    'args': vars(self.args)
                }
                
                os.makedirs('/data/TACL_chinese1_reset_v2/checkpoints', exist_ok=True)
                checkpoint_path = '/data/TACL_chinese1_reset_v2/checkpoints/ultra_level6_best_model.pth'
                torch.save(checkpoint, checkpoint_path)
                logging.info(f"💾 保存最佳Level 6模型: {checkpoint_path}")
                logging.info(f"🎯 Level 6准确率: {val_level6_acc:.4f} ({val_level6_acc*100:.2f}%)")
                
                # 检查是否达到目标
                if val_level6_acc >= 0.90:
                    logging.info(f"🎉 达到目标！Level 6准确率: {val_level6_acc*100:.2f}% >= 90%")
                    break
            else:
                patience_counter += 1
                logging.info(f"⏳ Level 6准确率未改善 ({patience_counter}/{self.args.patience})")
                if patience_counter >= self.args.patience:
                    logging.info(f"⏹️  早停：Level 6准确率连续{self.args.patience}个epoch未改善")
                    break
        
        self.writer.close()
        logging.info("🎉 超激进Level 6专门优化训练完成!")
        logging.info(f"🎯 最佳Level 6准确率: {best_level6_acc:.4f} ({best_level6_acc*100:.2f}%)")
        
        return best_level6_acc


def parse_args():
    parser = argparse.ArgumentParser(description='超激进Level 6专门优化训练')
    
    # 基本参数
    parser.add_argument('--model_path', type=str, default='/data/TACL_chinese1_reset_v2/Qwen3-0.6B')
    parser.add_argument('--max_length', type=int, default=512)
    parser.add_argument('--batch_size', type=int, default=8)
    parser.add_argument('--epochs', type=int, default=20)
    parser.add_argument('--seed', type=int, default=42)
    
    # 超激进学习率参数
    parser.add_argument('--level6_lr', type=float, default=1e-3)    # Level 6超高学习率
    parser.add_argument('--other_lr', type=float, default=1e-6)     # 其他参数极低学习率
    
    # 训练策略
    parser.add_argument('--freeze_early_levels', action='store_true', default=True)
    parser.add_argument('--use_amp', action='store_true', default=True)
    parser.add_argument('--patience', type=int, default=5)
    parser.add_argument('--num_workers', type=int, default=2)
    
    return parser.parse_args()


def main():
    args = parse_args()
    trainer = UltraLevel6Trainer(args)
    best_level6_acc = trainer.train()
    
    print(f"\n{'='*80}")
    print(f"🎯 超激进Level 6专门优化训练完成!")
    print(f"🎯 最佳Level 6准确率: {best_level6_acc:.4f} ({best_level6_acc*100:.2f}%)")
    if best_level6_acc >= 0.90:
        print(f"🎉 成功达到目标！Level 6准确率 >= 90%")
    else:
        print(f"📈 还需继续优化，目标是90%")
    print(f"{'='*80}")


if __name__ == "__main__":
    main()
