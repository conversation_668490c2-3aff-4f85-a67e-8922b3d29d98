#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Level 6专门优化的训练脚本
目标：将Level 6准确率从15.42%提升到90%以上
"""

import sys
import os
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import pickle
import json
import argparse
import logging
from transformers import AutoTokenizer
from transformers.optimization import get_cosine_schedule_with_warmup
from tqdm import tqdm
from torch.utils.tensorboard import SummaryWriter
from datetime import datetime
import random
from collections import defaultdict, Counter

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from src.models.hierarchical_model import HierarchicalQwenModel
from src.data.data_loader import create_data_loaders
from advanced_train import Level6FocusedLoss, FocalLoss

class Level6OptimizedTrainer:
    """专门针对Level 6优化的训练器"""
    
    def __init__(self, args):
        self.args = args
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 设置随机种子
        self.set_seed(args.seed)
        
        # 创建日志
        self.setup_logging()
        
        # 加载数据和模型
        self.setup_data()
        self.setup_model()
        self.setup_training()
    
    def set_seed(self, seed):
        """设置随机种子"""
        random.seed(seed)
        np.random.seed(seed)
        torch.manual_seed(seed)
        if torch.cuda.is_available():
            torch.cuda.manual_seed_all(seed)
    
    def setup_data(self):
        """设置数据加载器"""
        logging.info("📚 设置Level 6优化的数据加载器...")
        
        # 加载标签映射
        with open('/data/TACL_chinese1_reset_v2/processed_data/label_mappings.pkl', 'rb') as f:
            self.label_mappings = pickle.load(f)
        
        self.num_classes_per_level = [self.label_mappings[i]['num_classes'] for i in range(7)]
        
        # 创建tokenizer
        self.tokenizer = AutoTokenizer.from_pretrained(self.args.model_path)
        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token
        
        # 创建数据加载器 - 启用所有Level 6优化
        self.train_loader, self.val_loader, _ = create_data_loaders(
            'processed_data/train.json',
            'processed_data/val.json',
            'processed_data/test.json',
            self.tokenizer,
            batch_size=self.args.batch_size,
            max_length=self.args.max_length,
            num_workers=self.args.num_workers,
            use_balanced_sampling=True,
            use_level6_focus=True  # 启用Level 6专门优化
        )
        
        logging.info(f"✅ Level 6优化数据加载完成")
        logging.info(f"🏷️  各层级类别数: {self.num_classes_per_level}")
        logging.info(f"📊 训练批次数: {len(self.train_loader)}")
        logging.info(f"📊 验证批次数: {len(self.val_loader)}")
    
    def setup_model(self):
        """设置模型"""
        logging.info("🧠 创建Level 6优化模型...")
        
        self.model = HierarchicalQwenModel(
            model_path=self.args.model_path,
            num_classes_per_level=self.num_classes_per_level,
            max_length=self.args.max_length,
            dropout=0.1
        ).to(self.device)
        
        total_params = sum(p.numel() for p in self.model.parameters())
        trainable_params = sum(p.numel() for p in self.model.parameters() if p.requires_grad)
        
        logging.info(f"📊 模型总参数数量: {total_params:,}")
        logging.info(f"📊 可训练参数数量: {trainable_params:,}")
        logging.info(f"🖥️  设备: {self.device}")
    
    def setup_training(self):
        """设置训练组件"""
        logging.info("🎯 设置Level 6专门优化的训练组件...")
        
        # 使用Level 6专门优化的损失函数
        self.criterion = Level6FocusedLoss(
            self.num_classes_per_level,
            hierarchy_weight=0.2,  # 降低层次损失权重，专注分类
            level6_boost=5.0,      # Level 6权重大幅提升
            use_focal_for_level6=True
        )
        
        # 分层学习率 - Level 6相关参数使用更高学习率
        backbone_params = []
        level6_params = []
        other_classifier_params = []
        
        for name, param in self.model.named_parameters():
            if 'level6' in name.lower() or ('classifier' in name and '6' in name):
                level6_params.append(param)
            elif 'classifier' in name:
                other_classifier_params.append(param)
            else:
                backbone_params.append(param)
        
        self.optimizer = torch.optim.AdamW([
            {'params': backbone_params, 'lr': self.args.backbone_lr},
            {'params': other_classifier_params, 'lr': self.args.classifier_lr},
            {'params': level6_params, 'lr': self.args.classifier_lr * 2.0}  # Level 6参数更高学习率
        ], weight_decay=self.args.weight_decay)
        
        # 学习率调度器
        total_steps = len(self.train_loader) * self.args.epochs
        warmup_steps = int(total_steps * 0.1)  # 10%预热
        
        self.scheduler = get_cosine_schedule_with_warmup(
            self.optimizer,
            num_warmup_steps=warmup_steps,
            num_training_steps=total_steps
        )
        
        # 混合精度训练
        if self.args.use_amp:
            self.scaler = torch.cuda.amp.GradScaler()
        else:
            self.scaler = None
        
        logging.info(f"✅ Level 6专门优化训练设置完成")
        logging.info(f"🎯 Level 6参数数量: {sum(p.numel() for p in level6_params)}")
    
    def setup_logging(self):
        """设置日志"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 创建日志目录
        log_dir = f"/data/TACL_chinese1_reset_v2/results/training/level6_optimized_{timestamp}"
        os.makedirs(log_dir, exist_ok=True)
        
        # 设置文件日志
        log_file = os.path.join(log_dir, "training.log")
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        # TensorBoard日志
        tensorboard_dir = os.path.join(log_dir, "tensorboard")
        self.writer = SummaryWriter(log_dir=tensorboard_dir)
        
        # 保存配置
        config_file = os.path.join(log_dir, "config.json")
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(vars(self.args), f, indent=2, ensure_ascii=False)
        
        logging.info(f"📊 Level 6优化训练日志目录: {log_dir}")
        self.log_dir = log_dir
    
    def get_level6_focused_active_levels(self, epoch):
        """Level 6专门的课程学习策略"""
        # 更激进的课程学习，快速到达Level 6
        if epoch < 3:
            return [0, 1, 2, 3]  # 快速建立前4层基础
        elif epoch < 6:
            return [0, 1, 2, 3, 4, 5]  # 快速到达Level 5
        else:
            return list(range(len(self.num_classes_per_level)))  # 全力训练Level 6
    
    def train_epoch(self, epoch):
        """训练一个epoch"""
        self.model.train()
        total_loss = 0.0
        level6_correct = 0
        level6_total = 0
        
        # 获取当前应该训练的层级
        active_levels = self.get_level6_focused_active_levels(epoch)
        
        progress_bar = tqdm(self.train_loader, 
                           desc=f"Epoch {epoch+1}/{self.args.epochs} (Focus: Level 6)")
        
        for batch_idx, batch in enumerate(progress_bar):
            input_ids = batch['input_ids'].to(self.device)
            attention_mask = batch['attention_mask'].to(self.device)
            labels = batch['labels'].to(self.device)
            
            # 转换标签格式
            labels_list = [labels[:, i] for i in range(labels.size(1))]
            
            self.optimizer.zero_grad()
            
            if self.scaler:
                with torch.cuda.amp.autocast():
                    outputs = self.model(input_ids, attention_mask, labels_list)
                    # 只计算活跃层级的损失
                    active_logits = [outputs['logits'][i] for i in active_levels]
                    active_labels = [labels_list[i] for i in active_levels]
                    loss_dict = self.criterion(active_logits, active_labels)
                    loss = loss_dict['total_loss']
                
                self.scaler.scale(loss).backward()
                self.scaler.unscale_(self.optimizer)
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), 1.0)
                self.scaler.step(self.optimizer)
                self.scaler.update()
            else:
                outputs = self.model(input_ids, attention_mask, labels_list)
                active_logits = [outputs['logits'][i] for i in active_levels]
                active_labels = [labels_list[i] for i in active_levels]
                loss_dict = self.criterion(active_logits, active_labels)
                loss = loss_dict['total_loss']
                
                loss.backward()
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), 1.0)
                self.optimizer.step()
            
            self.scheduler.step()
            total_loss += loss.item()
            
            # 计算Level 6准确率
            if len(outputs['logits']) > 6:
                level6_pred = torch.argmax(outputs['logits'][6], dim=-1)
                level6_correct += (level6_pred == labels_list[6]).sum().item()
                level6_total += labels.size(0)
            
            # 更新进度条
            level6_acc = level6_correct / level6_total if level6_total > 0 else 0
            progress_bar.set_postfix({
                'Loss': f'{loss.item():.4f}',
                'Level6_Acc': f'{level6_acc:.4f}',
                'LR': f'{self.scheduler.get_last_lr()[0]:.2e}'
            })
            
            # 记录到TensorBoard
            global_step = epoch * len(self.train_loader) + batch_idx
            self.writer.add_scalar('Train/Loss', loss.item(), global_step)
            self.writer.add_scalar('Train/Level6_Acc', level6_acc, global_step)
            self.writer.add_scalar('Train/LR', self.scheduler.get_last_lr()[0], global_step)
        
        return total_loss / len(self.train_loader), level6_correct / level6_total if level6_total > 0 else 0

    def validate(self):
        """验证 - 特别关注Level 6性能"""
        self.model.eval()
        total_loss = 0.0
        correct_predictions = [0] * len(self.num_classes_per_level)
        total_predictions = 0

        # Level 6专门统计
        level6_correct = 0
        level6_total = 0
        level6_class_correct = defaultdict(int)
        level6_class_total = defaultdict(int)

        with torch.no_grad():
            for batch in tqdm(self.val_loader, desc="Validating (Focus: Level 6)"):
                input_ids = batch['input_ids'].to(self.device)
                attention_mask = batch['attention_mask'].to(self.device)
                labels = batch['labels'].to(self.device)

                labels_list = [labels[:, i] for i in range(labels.size(1))]

                outputs = self.model(input_ids, attention_mask, labels_list)
                loss_dict = self.criterion(outputs['logits'], labels_list)
                loss = loss_dict['total_loss']

                total_loss += loss.item()

                # 计算各层准确率
                for level, logits in enumerate(outputs['logits']):
                    predictions = torch.argmax(logits, dim=-1)
                    correct_predictions[level] += (predictions == labels_list[level]).sum().item()

                    # Level 6详细统计
                    if level == 6:
                        level6_pred = predictions
                        level6_true = labels_list[level]
                        level6_correct += (level6_pred == level6_true).sum().item()
                        level6_total += labels.size(0)

                        # 按类别统计
                        for true_label, pred_label in zip(level6_true.cpu().numpy(), level6_pred.cpu().numpy()):
                            level6_class_total[true_label] += 1
                            if true_label == pred_label:
                                level6_class_correct[true_label] += 1

                total_predictions += labels.size(0)

        avg_loss = total_loss / len(self.val_loader)
        accuracies = [correct / total_predictions for correct in correct_predictions]

        # Level 6详细分析
        level6_accuracy = level6_correct / level6_total if level6_total > 0 else 0
        level6_class_accuracies = {
            class_id: level6_class_correct[class_id] / level6_class_total[class_id]
            if level6_class_total[class_id] > 0 else 0
            for class_id in level6_class_total.keys()
        }

        return avg_loss, accuracies, level6_accuracy, level6_class_accuracies

    def train(self):
        """完整训练流程 - Level 6专门优化"""
        logging.info("🚀 开始Level 6专门优化训练...")
        logging.info(f"🎯 目标：Level 6准确率从15.42%提升到90%以上")
        logging.info(f"📱 设备: {self.device}")

        best_level6_acc = 0.0
        patience_counter = 0
        training_history = []

        for epoch in range(self.args.epochs):
            epoch_start_time = datetime.now()

            # 训练
            train_loss, train_level6_acc = self.train_epoch(epoch)

            # 验证
            val_loss, val_accuracies, val_level6_acc, level6_class_accs = self.validate()

            epoch_end_time = datetime.now()
            epoch_duration = (epoch_end_time - epoch_start_time).total_seconds()

            # 记录指标
            self.writer.add_scalar('Epoch/TrainLoss', train_loss, epoch)
            self.writer.add_scalar('Epoch/ValLoss', val_loss, epoch)
            self.writer.add_scalar('Epoch/TrainLevel6Acc', train_level6_acc, epoch)
            self.writer.add_scalar('Epoch/ValLevel6Acc', val_level6_acc, epoch)

            for level, acc in enumerate(val_accuracies):
                self.writer.add_scalar(f'Epoch/ValAcc_Level_{level}', acc, epoch)

            # 记录训练历史
            epoch_info = {
                'epoch': epoch + 1,
                'train_loss': train_loss,
                'val_loss': val_loss,
                'train_level6_acc': train_level6_acc,
                'val_level6_acc': val_level6_acc,
                'val_accuracies': val_accuracies,
                'level6_class_accuracies': level6_class_accs,
                'duration': epoch_duration,
                'timestamp': epoch_end_time.isoformat()
            }
            training_history.append(epoch_info)

            logging.info(f"\n{'='*80}")
            logging.info(f"Epoch {epoch+1}/{self.args.epochs} 完成 (耗时: {epoch_duration:.1f}s)")
            logging.info(f"训练损失: {train_loss:.4f}")
            logging.info(f"验证损失: {val_loss:.4f}")
            logging.info(f"🎯 Level 6训练准确率: {train_level6_acc:.4f} ({train_level6_acc*100:.2f}%)")
            logging.info(f"🎯 Level 6验证准确率: {val_level6_acc:.4f} ({val_level6_acc*100:.2f}%)")
            logging.info(f"验证准确率:")
            for level, acc in enumerate(val_accuracies):
                logging.info(f"Level {level}: {acc:.4f} ({acc*100:.2f}%)")

            # 保存最佳Level 6模型
            if val_level6_acc > best_level6_acc:
                best_level6_acc = val_level6_acc
                patience_counter = 0

                checkpoint = {
                    'epoch': epoch,
                    'model_state_dict': self.model.state_dict(),
                    'optimizer_state_dict': self.optimizer.state_dict(),
                    'val_loss': val_loss,
                    'val_accuracies': val_accuracies,
                    'val_level6_acc': val_level6_acc,
                    'level6_class_accuracies': level6_class_accs,
                    'args': vars(self.args),
                    'training_history': training_history
                }

                os.makedirs('/data/TACL_chinese1_reset_v2/checkpoints', exist_ok=True)
                checkpoint_path = '/data/TACL_chinese1_reset_v2/checkpoints/level6_optimized_best_model.pth'
                torch.save(checkpoint, checkpoint_path)
                logging.info(f"💾 保存最佳Level 6模型: {checkpoint_path}")
                logging.info(f"🎯 Level 6准确率: {val_level6_acc:.4f} ({val_level6_acc*100:.2f}%)")

                # 检查是否达到目标
                if val_level6_acc >= 0.90:
                    logging.info(f"🎉 达到目标！Level 6准确率: {val_level6_acc*100:.2f}% >= 90%")
                    break
            else:
                patience_counter += 1
                logging.info(f"⏳ Level 6准确率未改善 ({patience_counter}/{self.args.patience})")
                if patience_counter >= self.args.patience:
                    logging.info(f"⏹️  早停：Level 6准确率连续{self.args.patience}个epoch未改善")
                    break

        # 保存训练历史
        history_file = os.path.join(self.log_dir, "training_history.json")
        with open(history_file, 'w', encoding='utf-8') as f:
            json.dump(training_history, f, indent=2, ensure_ascii=False)

        self.writer.close()
        logging.info("🎉 Level 6专门优化训练完成!")
        logging.info(f"🎯 最佳Level 6准确率: {best_level6_acc:.4f} ({best_level6_acc*100:.2f}%)")
        logging.info(f"📊 训练历史已保存: {history_file}")

        return best_level6_acc


def parse_args():
    parser = argparse.ArgumentParser(description='Level 6专门优化训练')

    # 基本参数
    parser.add_argument('--model_path', type=str, default='/data/TACL_chinese1_reset_v2/Qwen3-0.6B')
    parser.add_argument('--max_length', type=int, default=512)
    parser.add_argument('--batch_size', type=int, default=6)  # 稍小的batch size以适应更复杂的模型
    parser.add_argument('--epochs', type=int, default=50)     # 更多epochs专门优化Level 6
    parser.add_argument('--seed', type=int, default=42)

    # 学习率参数 - 针对Level 6优化
    parser.add_argument('--backbone_lr', type=float, default=5e-6)    # 更低的backbone学习率
    parser.add_argument('--classifier_lr', type=float, default=2e-4)  # 更高的分类器学习率
    parser.add_argument('--weight_decay', type=float, default=0.01)

    # 训练策略
    parser.add_argument('--use_amp', action='store_true', default=True)
    parser.add_argument('--patience', type=int, default=10)
    parser.add_argument('--num_workers', type=int, default=2)

    return parser.parse_args()


def main():
    args = parse_args()
    trainer = Level6OptimizedTrainer(args)
    best_level6_acc = trainer.train()

    print(f"\n{'='*80}")
    print(f"🎯 Level 6专门优化训练完成!")
    print(f"🎯 最佳Level 6准确率: {best_level6_acc:.4f} ({best_level6_acc*100:.2f}%)")
    if best_level6_acc >= 0.90:
        print(f"🎉 成功达到目标！Level 6准确率 >= 90%")
    else:
        print(f"📈 还需继续优化，目标是90%")
    print(f"{'='*80}")


if __name__ == "__main__":
    main()
