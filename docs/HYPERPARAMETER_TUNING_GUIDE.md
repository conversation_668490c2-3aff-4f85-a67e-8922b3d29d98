# 超参数调优指南 - 为小白用户设计

## 🎯 概述

这个指南专门为不熟悉深度学习超参数调优的用户设计，提供了自动化的超参数搜索工具，帮您找到最优的训练配置。

## 🚀 快速开始

### 1. **智能搜索（推荐新手）**
```bash
cd /data/TACL_chinese1_reset_v2
/home/<USER>/anaconda3/envs/htc/bin/python hyperparameter_search.py --search_type smart
```

这会自动测试3个精心设计的配置组合，通常能找到不错的结果。

### 2. **快速测试模式**
```bash
/home/<USER>/anaconda3/envs/htc/bin/python hyperparameter_search.py --search_type smart --quick_mode
```

用于快速验证，训练时间更短，适合初次尝试。

### 3. **网格搜索（最全面）**
```bash
/home/<USER>/anaconda3/envs/htc/bin/python hyperparameter_search.py --search_type grid
```

会测试所有可能的组合，时间较长但结果最全面。

## 📊 搜索策略对比

| 搜索类型 | 配置数量 | 预计时间 | 适用场景 |
|----------|----------|----------|----------|
| **smart** | 3个 | 6-12小时 | **新手推荐** |
| **random** | 8-20个 | 16-40小时 | 中等探索 |
| **grid** | 48-96个 | 2-4天 | 完全搜索 |

## ⚙️ 搜索的超参数

### **核心超参数说明**

1. **backbone_lr** (Backbone学习率)
   - **作用**: 控制预训练模型参数的更新速度
   - **范围**: 1e-6 到 2e-5
   - **建议**: 新手选择较小值（5e-6）更安全

2. **classifier_lr** (分类器学习率)
   - **作用**: 控制新增分类层的更新速度
   - **范围**: 5e-5 到 5e-4
   - **建议**: 通常比backbone_lr大10倍

3. **hierarchy_weight** (层次损失权重)
   - **作用**: 控制层次一致性约束的强度
   - **范围**: 0.1 到 0.7
   - **建议**: 0.3-0.5 通常效果较好

4. **batch_size** (批次大小)
   - **作用**: 每次训练使用的样本数量
   - **选项**: 2 或 4
   - **建议**: 显存足够选4，否则选2

5. **epochs** (训练轮数)
   - **作用**: 完整训练数据的遍历次数
   - **范围**: 5 到 15
   - **建议**: 新手从8开始

6. **warmup_ratio** (预热比例)
   - **作用**: 学习率预热阶段的比例
   - **范围**: 0.05 到 0.15
   - **建议**: 0.1 是经典选择

## 📈 评估指标

搜索脚本使用**综合分数**来评估配置：

```
综合分数 = 整体准确率 × 0.4 + Level1准确率 × 0.3 + Level2准确率 × 0.2 + 层次一致性 × 0.1
```

**分数越高越好**，通常：
- 0.3-0.4: 一般效果
- 0.4-0.5: 良好效果  
- 0.5+: 优秀效果

## 📁 结果文件

### **日志文件位置**
```
/data/TACL_chinese1_reset_v2/results/hyperparameter_search/
├── hyperparameter_search_YYYYMMDD_HHMMSS.log    # 详细日志
└── search_results_YYYYMMDD_HHMMSS.json          # 搜索结果
```

### **结果文件内容**
```json
{
  "search_type": "smart",
  "best_config": {
    "backbone_lr": 1e-5,
    "classifier_lr": 2e-4,
    "hierarchy_weight": 0.5,
    "batch_size": 4,
    "epochs": 12,
    "warmup_ratio": 0.1
  },
  "best_score": 0.4523,
  "total_trials": 3,
  "successful_trials": 3,
  "results": [...]
}
```

## 🎯 使用最佳配置

找到最佳配置后，使用以下命令进行正式训练：

```bash
/home/<USER>/anaconda3/envs/htc/bin/python advanced_train.py \
    --loss_type hierarchical \
    --use_class_weights \
    --curriculum_learning \
    --use_amp \
    --backbone_lr 1e-5 \
    --classifier_lr 2e-4 \
    --hierarchy_weight 0.5 \
    --batch_size 4 \
    --epochs 12 \
    --warmup_ratio 0.1
```

## ⚠️ 注意事项

### **显存要求**
- **batch_size=4**: 需要约12GB显存
- **batch_size=2**: 需要约8GB显存
- 如果显存不足，脚本会自动失败并记录

### **时间估算**
- 每个配置大约需要2-4小时训练
- 智能搜索(3个配置): 6-12小时
- 网格搜索(48个配置): 4-8天

### **中断恢复**
- 搜索过程中可以随时中断（Ctrl+C）
- 已完成的试验结果会自动保存
- 可以查看中间结果文件了解进度

## 🔧 故障排除

### **常见问题**

1. **显存不足**
   ```
   解决方案: 使用 --quick_mode 或手动设置 batch_size=2
   ```

2. **训练失败**
   ```
   检查: 模型文件路径是否正确
   检查: 数据文件是否存在
   ```

3. **搜索时间太长**
   ```
   建议: 先用 smart + quick_mode 测试
   建议: 确认配置后再用完整搜索
   ```

## 📊 结果解读

### **好的结果特征**
- Level 1准确率 > 90%
- Level 2准确率 > 15%
- 层次一致性 > 50%
- 综合分数 > 0.45

### **需要改进的信号**
- Level 2准确率 < 5%
- 层次一致性 < 30%
- 训练损失不收敛
- 验证损失持续上升

## 🎉 成功案例

### **典型的优秀配置**
```json
{
  "backbone_lr": 5e-6,
  "classifier_lr": 1e-4,
  "hierarchy_weight": 0.5,
  "batch_size": 4,
  "epochs": 12,
  "warmup_ratio": 0.1
}
```

**预期效果**:
- Level 1准确率: 92%+
- Level 2准确率: 18%+
- 层次一致性: 60%+
- 综合分数: 0.47+

## 💡 进阶技巧

### **手动调优建议**
1. **如果Level 1效果差**: 降低backbone_lr
2. **如果深层效果差**: 增加hierarchy_weight
3. **如果训练不稳定**: 降低学习率，增加warmup_ratio
4. **如果过拟合**: 减少epochs，增加正则化

### **自定义搜索空间**
可以修改 `hyperparameter_search.py` 中的 `search_space` 来自定义搜索范围。

---

**这个工具让超参数调优变得简单！即使是深度学习新手也能找到最优配置。** 🚀
