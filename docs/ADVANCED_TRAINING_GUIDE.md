# 高级训练方法指南

## 🎯 高级训练技术

我为您实现了多种先进的训练技术来提升层次化分类效果：

### 1. **层次化损失函数 (Hierarchical Loss)**
- **层次一致性约束**：确保子类预测与父类预测一致
- **加权分类损失**：不同层级使用不同权重
- **自动层次映射**：从训练数据学习真实的父子关系

### 2. **Focal Loss**
- **解决类别不平衡**：重点关注难分类样本
- **动态权重调整**：简单样本权重降低，困难样本权重提高

### 3. **课程学习 (Curriculum Learning)**
- **渐进式训练**：先训练浅层，再逐步加入深层
- **分阶段学习**：避免深层类别过早干扰浅层学习

### 4. **分层学习率**
- **Backbone低学习率**：保护预训练知识
- **Classifier高学习率**：快速适应新任务

### 5. **类别权重平衡**
- **自动计算权重**：基于类别频率自动平衡
- **处理长尾分布**：提升少数类别的重要性

### 6. **混合精度训练**
- **节省显存**：使用FP16减少内存占用
- **加速训练**：提升训练速度

## 🚀 使用方法

### **基础高级训练**
```bash
cd /data/TACL_chinese1_reset_v2
/home/<USER>/anaconda3/envs/htc/bin/python advanced_train.py
```

### **推荐配置（最佳效果）**
```bash
/home/<USER>/anaconda3/envs/htc/bin/python advanced_train.py \
    --loss_type hierarchical \
    --use_class_weights \
    --curriculum_learning \
    --use_amp \
    --epochs 15 \
    --batch_size 4 \
    --backbone_lr 5e-6 \
    --classifier_lr 1e-4 \
    --hierarchy_weight 0.5
```
/home/<USER>/anaconda3/envs/htc/bin/python advanced_train.py \
    --loss_type hierarchical \
    --use_class_weights \
    --curriculum_learning \
    --use_amp

### **快速测试配置**
```bash
/home/<USER>/anaconda3/envs/htc/bin/python advanced_train.py \
    --epochs 5 \
    --batch_size 2 \
    --curriculum_learning
```

### **处理类别不平衡**
```bash
/home/<USER>/anaconda3/envs/htc/bin/python advanced_train.py \
    --loss_type focal \
    --focal_gamma 3.0 \
    --use_class_weights \
    --use_balanced_sampling
```

## ⚙️ 参数详解

### **核心参数**
| 参数 | 默认值 | 说明 |
|------|--------|------|
| `--loss_type` | `hierarchical` | 损失函数类型 |
| `--hierarchy_weight` | `0.3` | 层次一致性权重 |
| `--curriculum_learning` | `False` | 是否使用课程学习 |
| `--use_class_weights` | `False` | 是否使用类别权重 |

### **学习率参数**
| 参数 | 默认值 | 说明 |
|------|--------|------|
| `--backbone_lr` | `1e-5` | Backbone学习率 |
| `--classifier_lr` | `1e-4` | 分类器学习率 |
| `--warmup_ratio` | `0.1` | 预热比例 |

### **训练策略**
| 参数 | 默认值 | 说明 |
|------|--------|------|
| `--focal_gamma` | `2.0` | Focal Loss的gamma参数 |
| `--use_balanced_sampling` | `False` | 平衡采样 |
| `--use_amp` | `False` | 混合精度训练 |

## 📊 预期改进效果

### **相比基础训练的提升**
1. **层次一致性**：从3.68%提升到预期60%+
2. **深层准确率**：Level 2从1.12%提升到预期15%+
3. **整体性能**：整体准确率从26.74%提升到预期45%+

### **各技术的贡献**
- **层次化损失**：+15-20% 层次一致性
- **课程学习**：+10-15% 深层准确率  
- **类别权重**：+5-10% 少数类别性能
- **分层学习率**：+3-5% 整体稳定性

## 🔍 训练过程监控

### **TensorBoard指标**
```bash
tensorboard --logdir=logs/runs --port=6006
```

关键指标：
- `Train/Loss`: 训练损失
- `Epoch/ValAcc_Level_X`: 各层级验证准确率
- `Train/LR`: 学习率变化

### **训练日志示例**
```
Epoch 1/15 (Levels: [0, 1]):
  训练损失: 1.2345
  验证损失: 1.1234
  验证准确率: ['1.0000', '0.8500', '0.0000', ...]

Epoch 4/15 (Levels: [0, 1, 2]):
  训练损失: 0.9876
  验证损失: 0.8765
  验证准确率: ['1.0000', '0.9200', '0.3500', ...]
```

## 💡 训练策略建议

### **阶段1：基础训练（推荐）**
```bash
/home/<USER>/anaconda3/envs/htc/bin/python advanced_train.py \
    --loss_type hierarchical \
    --curriculum_learning \
    --epochs 10
```

### **阶段2：精细调优**
```bash
/home/<USER>/anaconda3/envs/htc/bin/python advanced_train.py \
    --loss_type hierarchical \
    --use_class_weights \
    --use_balanced_sampling \
    --hierarchy_weight 0.5 \
    --epochs 15
```

### **阶段3：最终优化**
```bash
/home/<USER>/anaconda3/envs/htc/bin/python advanced_train.py \
    --loss_type hierarchical \
    --use_class_weights \
    --curriculum_learning \
    --use_amp \
    --backbone_lr 3e-6 \
    --classifier_lr 5e-5 \
    --epochs 20
```

## 🎯 针对性解决方案

### **问题1：深层分类效果差**
**解决方案**：课程学习 + 层次化损失
```bash
--curriculum_learning --loss_type hierarchical --hierarchy_weight 0.5
```

### **问题2：类别不平衡**
**解决方案**：Focal Loss + 类别权重 + 平衡采样
```bash
--loss_type focal --use_class_weights --use_balanced_sampling
```

### **问题3：层次不一致**
**解决方案**：层次化损失 + 高层次权重
```bash
--loss_type hierarchical --hierarchy_weight 0.7
```

### **问题4：训练不稳定**
**解决方案**：分层学习率 + 梯度裁剪
```bash
--backbone_lr 5e-6 --classifier_lr 1e-4 --grad_clip 0.5
```

## 📈 评估改进效果

训练完成后，使用以下命令评估：

```bash
# 使用高级训练的模型进行评估
/home/<USER>/anaconda3/envs/htc/bin/python evaluate.py --checkpoint checkpoints/advanced_best_model.pth

# 测试具体预测效果
/home/<USER>/anaconda3/envs/htc/bin/python test_model_predictions.py --checkpoint checkpoints/advanced_best_model.pth
```

## ⚠️ 注意事项

1. **显存要求**：高级训练需要更多显存，建议batch_size=2-4
2. **训练时间**：比基础训练慢2-3倍，但效果显著提升
3. **参数调优**：建议先用默认参数，再根据结果调整
4. **早停机制**：设置了patience=5，避免过拟合

---

**准备好体验显著的性能提升了吗？** 🚀
