# 模型预测测试指南

## 🎯 功能说明

`test_model_predictions.py` 是一个专门用于测试训练后模型实际分类效果的脚本，可以直观地查看模型对具体文本的层次化分类结果。

## 🚀 使用方法

### 1. 基本使用（推荐）
```bash
cd /data/TACL_chinese1_reset
/home/<USER>/anaconda3/envs/htc/bin/python test_model_predictions.py
```

### 2. 指定模型检查点
```bash
# 使用最佳模型
/home/<USER>/anaconda3/envs/htc/bin/python test_model_predictions.py --checkpoint checkpoints/best_model.pth

# 使用最终模型
/home/<USER>/anaconda3/envs/htc/bin/python test_model_predictions.py --checkpoint checkpoints/final_model.pth

# 使用快速训练模型
/home/<USER>/anaconda3/envs/htc/bin/python test_model_predictions.py --checkpoint checkpoints/quick_train_model.pth
```

### 3. 选择测试模式
```bash
# 只测试示例文本
/home/<USER>/anaconda3/envs/htc/bin/python test_model_predictions.py --mode sample

# 只测试数据集样本
/home/<USER>/anaconda3/envs/htc/bin/python test_model_predictions.py --mode dataset --num_samples 5

# 只进入交互模式
/home/<USER>/anaconda3/envs/htc/bin/python test_model_predictions.py --mode interactive

# 运行所有模式（默认）
/home/<USER>/anaconda3/envs/htc/bin/python test_model_predictions.py --mode all
```

## 📋 测试模式详解

### 🧪 **示例文本测试 (sample)**
- 使用预设的6个典型小学语文题目
- 涵盖拼音、阅读、写作、词汇、语法等不同类型
- 快速了解模型的基本分类能力

**示例输出**:
```
📝 示例 1: 小明学会了拼音字母a、o、e的发音。
🎯 预测结果:
  Level 0: 小学语文知识树 (类别ID: 0, 置信度: 1.0000)
  Level 1: 基础知识 (类别ID: 0, 置信度: 0.8234)
  Level 2: 拼音 (类别ID: 0, 置信度: 0.6789)
  ...
```

### 📊 **数据集样本测试 (dataset)**
- 从测试数据集中随机选择样本
- 显示预测结果 vs 真实标签的对比
- 计算样本级别的准确率统计
- 可以指定测试样本数量

**示例输出**:
```
📝 样本 1:
文本: 请根据课文内容回答问题...
🎯 预测 vs 真实:
  Level 0: ✅ 预测=小学语文知识树 | 真实=小学语文知识树 (置信度: 1.0000)
  Level 1: ✅ 预测=阅读鉴赏 | 真实=阅读鉴赏 (置信度: 0.7234)
  Level 2: ❌ 预测=词语 | 真实=绘本阅读 (置信度: 0.4567)
  ...
```

### 🎮 **交互式测试 (interactive)**
- 可以输入任意文本进行实时分类
- 适合测试特定的文本内容
- 输入 'quit' 或 'exit' 退出

**使用示例**:
```
请输入要分类的文本: 这个字的拼音是什么？
🎯 预测结果:
  Level 0: 小学语文知识树 (类别ID: 0, 置信度: 1.0000)
  Level 1: 基础知识 (类别ID: 0, 置信度: 0.9123)
  Level 2: 拼音 (类别ID: 0, 置信度: 0.8456)
  ...
```

## ⚙️ 参数说明

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `--model_path` | `Qwen3-0.6B` | 预训练模型路径 |
| `--checkpoint` | `checkpoints/best_model.pth` | 模型检查点路径 |
| `--max_length` | `256` | 最大序列长度 |
| `--mode` | `all` | 测试模式 |
| `--num_samples` | `10` | 数据集测试样本数 |

## 📊 输出信息解读

### 🏷️ **标签信息**
- **类别ID**: 模型预测的数字标签（0, 1, 2, ...）
- **标签名称**: 对应的中文标签名称
- **置信度**: 模型对该预测的信心程度（0-1之间）

### 📈 **准确率统计**
- **Level 0-6**: 各层级的预测准确率
- **✅/❌**: 预测正确/错误的标识
- **样本统计**: 正确数量/总数量 = 准确率

## 🔍 **典型使用场景**

### 1. **快速验证模型效果**
```bash
/home/<USER>/anaconda3/envs/htc/bin/python test_model_predictions.py --mode sample
```

### 2. **详细分析模型性能**
```bash
/home/<USER>/anaconda3/envs/htc/bin/python test_model_predictions.py --mode dataset --num_samples 20
```

### 3. **测试特定文本**
```bash
/home/<USER>/anaconda3/envs/htc/bin/python test_model_predictions.py --mode interactive
```

### 4. **比较不同模型**
```bash
# 测试最佳模型
/home/<USER>/anaconda3/envs/htc/bin/python test_model_predictions.py --checkpoint checkpoints/best_model.pth --mode sample

# 测试快速训练模型
/home/<USER>/anaconda3/envs/htc/bin/python test_model_predictions.py --checkpoint checkpoints/quick_train_model.pth --mode sample
```

## ⚠️ **注意事项**

1. **模型文件**: 确保指定的检查点文件存在
2. **设备要求**: 自动检测GPU/CPU，GPU训练的模型在CPU上也能运行
3. **文本长度**: 超过max_length的文本会被截断
4. **中文支持**: 完全支持中文文本输入和输出

## 🎯 **预期结果**

根据之前的评估结果，您可能会看到：
- **Level 0-1**: 预测效果较好，准确率较高
- **Level 2+**: 预测效果较差，经常出现错误分类
- **置信度**: 浅层置信度较高，深层置信度较低

这个测试脚本将帮助您直观地理解模型的实际表现！

---

**现在您可以直观地查看模型的分类效果了！** 🎉
