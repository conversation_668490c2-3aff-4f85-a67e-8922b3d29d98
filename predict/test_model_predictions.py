#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模型预测测试脚本 - 查看实际分类标签输出结果
"""

import sys
import os
import torch
import pickle
import json
import argparse
import logging
from datetime import datetime
from transformers import AutoTokenizer
import numpy as np

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from src.models.hierarchical_model import HierarchicalQwenModel

class ModelTester:
    def __init__(self, model_path, checkpoint_path, max_length=512):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.max_length = max_length
        
        # 加载标签映射
        with open('/data/TACL_chinese1_reset_v2/processed_data/label_mappings.pkl', 'rb') as f:
            self.label_mappings = pickle.load(f)
        
        self.num_classes_per_level = [self.label_mappings[i]['num_classes'] for i in range(7)]
        
        # 创建tokenizer
        self.tokenizer = AutoTokenizer.from_pretrained(model_path)
        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token
        
        # 创建并加载模型
        self.model = HierarchicalQwenModel(
            model_path=model_path,
            num_classes_per_level=self.num_classes_per_level,
            max_length=max_length
        ).to(self.device)
        
        # 加载训练好的权重
        self.load_checkpoint(checkpoint_path)
        self.model.eval()
        
        print(f"✅ 模型加载成功")
        print(f"📱 设备: {self.device}")
        print(f"🏷️  各层级类别数: {self.num_classes_per_level}")
    
    def load_checkpoint(self, checkpoint_path):
        """加载模型检查点"""
        if not os.path.exists(checkpoint_path):
            print(f"❌ 检查点文件不存在: {checkpoint_path}")
            print("🔍 可用的检查点文件:")
            checkpoint_dir = os.path.dirname(checkpoint_path)
            if os.path.exists(checkpoint_dir):
                for file in os.listdir(checkpoint_dir):
                    if file.endswith('.pth'):
                        print(f"  - {os.path.join(checkpoint_dir, file)}")
            return
        
        try:
            checkpoint = torch.load(checkpoint_path, map_location=self.device, weights_only=False)
            if 'model_state_dict' in checkpoint:
                self.model.load_state_dict(checkpoint['model_state_dict'])
                print(f"📥 成功加载检查点: {checkpoint_path}")
                if 'epoch' in checkpoint:
                    print(f"📊 训练轮数: {checkpoint['epoch']}")
                if 'val_loss' in checkpoint:
                    print(f"📈 验证损失: {checkpoint['val_loss']:.4f}")
            else:
                # 简单的state_dict格式
                self.model.load_state_dict(torch.load(checkpoint_path, map_location=self.device, weights_only=True))
                print(f"📥 成功加载简单格式检查点: {checkpoint_path}")
        except Exception as e:
            print(f"❌ 加载检查点失败: {e}")
    
    def predict_single_text(self, text):
        """预测单个文本的分类结果"""
        # 编码文本
        encoding = self.tokenizer(
            text,
            truncation=True,
            padding='max_length',
            max_length=self.max_length,
            return_tensors='pt'
        )
        
        input_ids = encoding['input_ids'].to(self.device)
        attention_mask = encoding['attention_mask'].to(self.device)
        
        # 预测
        with torch.no_grad():
            outputs = self.model(input_ids, attention_mask)
            logits = outputs['logits']
            
            # 获取每层的预测结果
            predictions = []
            probabilities = []
            
            for level, level_logits in enumerate(logits):
                # 获取预测类别
                pred_probs = torch.softmax(level_logits, dim=-1)
                pred_class = torch.argmax(level_logits, dim=-1).item()
                pred_prob = pred_probs[0, pred_class].item()
                
                predictions.append(pred_class)
                probabilities.append(pred_prob)
        
        return predictions, probabilities
    
    def get_label_names(self, predictions):
        """将预测的数字标签转换为文本标签"""
        label_names = []
        for level, pred_class in enumerate(predictions):
            idx_to_label = self.label_mappings[level]['idx_to_label']
            label_name = idx_to_label.get(pred_class, f'Unknown_Class_{pred_class}')
            label_names.append(label_name)
        return label_names
    
    def test_sample_texts(self):
        """测试一些示例文本"""
        sample_texts = [
            "小明学会了拼音字母a、o、e的发音。",
            "这篇文章的主要内容是什么？请用自己的话概括。",
            "请写一篇关于春天的作文，不少于200字。",
            "词语'美丽'的近义词有哪些？",
            "句子'小鸟在树上唱歌'中的主语是什么？",
            "阅读下面的故事，回答问题：从前有一只小兔子..."
        ]
        
        print("\n" + "="*80)
        print("🧪 示例文本预测结果")
        print("="*80)
        
        for i, text in enumerate(sample_texts, 1):
            print(f"\n📝 示例 {i}: {text}")
            print("-" * 60)
            
            predictions, probabilities = self.predict_single_text(text)
            label_names = self.get_label_names(predictions)
            
            print("🎯 预测结果:")
            for level in range(len(predictions)):
                print(f"  Level {level}: {label_names[level]} (类别ID: {predictions[level]}, 置信度: {probabilities[level]:.4f})")
    
    def test_from_dataset(self, num_samples=10):
        """从测试数据集中随机选择样本进行测试"""
        try:
            with open('/data/TACL_chinese1_reset_v2/processed_data/test.json', 'r', encoding='utf-8') as f:
                test_data = json.load(f)
            
            # 随机选择样本
            import random
            random.seed(42)
            selected_samples = random.sample(test_data, min(num_samples, len(test_data)))
            
            print("\n" + "="*80)
            print(f"📊 测试数据集样本预测结果 (随机选择{len(selected_samples)}个)")
            print("="*80)
            
            correct_predictions = [0] * 7  # 每层的正确预测数
            total_predictions = len(selected_samples)
            
            for i, sample in enumerate(selected_samples, 1):
                text = sample['text']
                true_labels = sample['labels']
                
                print(f"\n📝 样本 {i}:")
                print(f"文本: {text[:100]}{'...' if len(text) > 100 else ''}")
                print("-" * 60)
                
                predictions, probabilities = self.predict_single_text(text)
                pred_label_names = self.get_label_names(predictions)
                true_label_names = self.get_label_names(true_labels)
                
                print("🎯 预测 vs 真实:")
                for level in range(len(predictions)):
                    is_correct = predictions[level] == true_labels[level]
                    if is_correct:
                        correct_predictions[level] += 1
                    
                    status = "✅" if is_correct else "❌"
                    print(f"  Level {level}: {status} 预测={pred_label_names[level]} | 真实={true_label_names[level]} (置信度: {probabilities[level]:.4f})")
            
            # 计算准确率
            print("\n" + "="*60)
            print("📈 样本准确率统计:")
            print("="*60)
            for level in range(7):
                accuracy = correct_predictions[level] / total_predictions
                print(f"Level {level}: {correct_predictions[level]}/{total_predictions} = {accuracy:.4f} ({accuracy*100:.2f}%)")
                
        except Exception as e:
            print(f"❌ 读取测试数据失败: {e}")
    
    def interactive_test(self):
        """交互式测试模式"""
        print("\n" + "="*80)
        print("🎮 交互式测试模式")
        print("输入文本进行分类预测，输入 'quit' 退出")
        print("="*80)
        
        while True:
            try:
                text = input("\n请输入要分类的文本: ").strip()
                if text.lower() in ['quit', 'exit', '退出']:
                    print("👋 退出交互模式")
                    break
                
                if not text:
                    print("⚠️  请输入有效文本")
                    continue
                
                print(f"\n📝 输入文本: {text}")
                print("-" * 60)
                
                predictions, probabilities = self.predict_single_text(text)
                label_names = self.get_label_names(predictions)
                
                print("🎯 预测结果:")
                for level in range(len(predictions)):
                    print(f"  Level {level}: {label_names[level]} (类别ID: {predictions[level]}, 置信度: {probabilities[level]:.4f})")
                    
            except KeyboardInterrupt:
                print("\n👋 退出交互模式")
                break
            except Exception as e:
                print(f"❌ 预测失败: {e}")

def main():
    parser = argparse.ArgumentParser(description='模型预测测试')
    parser.add_argument('--model_path', type=str, default='/data/TACL_chinese1_reset_v2/Qwen3-0.6B',
                       help='预训练模型路径')
    parser.add_argument('--checkpoint', type=str, default='/data/TACL_chinese1_reset_v2/checkpoints/advanced_best_model.pth',
                       help='模型检查点路径')
    parser.add_argument('--max_length', type=int, default=512,
                       help='最大序列长度')
    parser.add_argument('--mode', type=str, default='dataset', 
                       choices=['sample', 'dataset', 'interactive', 'all'],
                       help='测试模式: sample(示例文本), dataset(数据集样本), interactive(交互式), all(全部)')
    parser.add_argument('--num_samples', type=int, default=10,
                       help='从数据集测试的样本数量')
    
    args = parser.parse_args()
    
    # 创建测试器
    tester = ModelTester(
        model_path=args.model_path,
        checkpoint_path=args.checkpoint,
        max_length=args.max_length
    )
    
    # 根据模式运行测试
    if args.mode in ['sample', 'all']:
        tester.test_sample_texts()
    
    if args.mode in ['dataset', 'all']:
        tester.test_from_dataset(args.num_samples)
    
    if args.mode in ['interactive', 'all']:
        tester.interactive_test()

if __name__ == "__main__":
    main()
