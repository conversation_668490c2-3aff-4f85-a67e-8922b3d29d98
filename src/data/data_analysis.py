#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据分析和预处理脚本
用于深入分析七层级标签分类数据集
"""

import json
import numpy as np
import pandas as pd
from collections import defaultdict, Counter
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.preprocessing import LabelEncoder
import pickle
import os

class HierarchicalDataAnalyzer:
    def __init__(self, data_dir='data'):
        self.data_dir = data_dir
        self.train_data = None
        self.val_data = None
        self.test_data = None
        self.label_encoders = {}
        self.label_mappings = {}
        self.hierarchy_stats = {}

    def load_data(self):
        """加载数据集"""
        print("加载数据集...")

        with open(f'{self.data_dir}/wos_train.json', 'r', encoding='utf-8') as f:
            self.train_data = json.load(f)

        with open(f'{self.data_dir}/wos_val.json', 'r', encoding='utf-8') as f:
            self.val_data = json.load(f)

        with open(f'{self.data_dir}/wos_test.json', 'r', encoding='utf-8') as f:
            self.test_data = json.load(f)

        print(f"训练集: {len(self.train_data)} 样本")
        print(f"验证集: {len(self.val_data)} 样本")
        print(f"测试集: {len(self.test_data)} 样本")

    def build_label_mappings(self):
        """构建标签映射"""
        print("构建标签映射...")

        # 为每一层构建标签映射
        for level in range(7):
            with open(f'{self.data_dir}/formatted_data/label{level}.txt', 'r', encoding='utf-8') as f:
                labels = [line.strip() for line in f.readlines() if line.strip()]

            # 创建标签到索引的映射
            label_to_idx = {label: idx for idx, label in enumerate(labels)}
            idx_to_label = {idx: label for idx, label in enumerate(labels)}

            self.label_mappings[level] = {
                'label_to_idx': label_to_idx,
                'idx_to_label': idx_to_label,
                'num_classes': len(labels)
            }

            print(f"第{level}层: {len(labels)} 个标签")

    def analyze_hierarchy_structure(self):
        """分析层次结构"""
        print("分析层次结构...")

        # 统计每层标签的分布
        level_distributions = defaultdict(Counter)
        hierarchy_paths = defaultdict(int)

        all_data = self.train_data + self.val_data + self.test_data

        for sample in all_data:
            labels = sample['doc_label']

            # 统计每层标签分布
            for level, label in enumerate(labels):
                level_distributions[level][label] += 1

            # 统计层次路径
            path = ' -> '.join(labels)
            hierarchy_paths[path] += 1

        self.hierarchy_stats = {
            'level_distributions': dict(level_distributions),
            'hierarchy_paths': dict(hierarchy_paths),
            'unique_paths': len(hierarchy_paths)
        }

        print(f"唯一的层次路径数量: {len(hierarchy_paths)}")

        # 分析标签不平衡情况
        for level in range(7):
            dist = level_distributions[level]
            values = list(dist.values())
            print(f"第{level}层标签分布 - 最大: {max(values)}, 最小: {min(values)}, 平均: {np.mean(values):.2f}")

    def analyze_text_statistics(self):
        """分析文本统计信息"""
        print("分析文本统计信息...")

        all_data = self.train_data + self.val_data + self.test_data
        text_lengths = []

        for sample in all_data:
            text = sample['doc_token']
            text_lengths.append(len(text))

        print(f"文本长度统计:")
        print(f"  平均长度: {np.mean(text_lengths):.2f}")
        print(f"  中位数长度: {np.median(text_lengths):.2f}")
        print(f"  最大长度: {max(text_lengths)}")
        print(f"  最小长度: {min(text_lengths)}")
        print(f"  标准差: {np.std(text_lengths):.2f}")

        # 分析长度分布的百分位数
        percentiles = [25, 50, 75, 90, 95, 99]
        for p in percentiles:
            print(f"  {p}%分位数: {np.percentile(text_lengths, p):.0f}")

    def create_processed_datasets(self):
        """创建处理后的数据集"""
        print("创建处理后的数据集...")

        def process_dataset(data, split_name):
            processed_data = []

            for sample in data:
                text = sample['doc_token']
                labels = sample['doc_label']

                # 将标签转换为索引
                label_indices = []
                for level, label in enumerate(labels):
                    if label in self.label_mappings[level]['label_to_idx']:
                        idx = self.label_mappings[level]['label_to_idx'][label]
                        label_indices.append(idx)
                    else:
                        print(f"警告: 标签 '{label}' 在第{level}层中未找到")
                        label_indices.append(0)  # 使用默认索引

                processed_sample = {
                    'text': text,
                    'labels': label_indices,
                    'original_labels': labels
                }
                processed_data.append(processed_sample)

            return processed_data

        # 处理各个数据集
        processed_train = process_dataset(self.train_data, 'train')
        processed_val = process_dataset(self.val_data, 'val')
        processed_test = process_dataset(self.test_data, 'test')

        # 保存处理后的数据
        os.makedirs('processed_data', exist_ok=True)

        with open('/data/TACL_chinese1_reset_v2/processed_data/train.json', 'w', encoding='utf-8') as f:
            json.dump(processed_train, f, ensure_ascii=False, indent=2)

        with open('/data/TACL_chinese1_reset_v2/processed_data/val.json', 'w', encoding='utf-8') as f:
            json.dump(processed_val, f, ensure_ascii=False, indent=2)

        with open('/data/TACL_chinese1_reset_v2/processed_data/test.json', 'w', encoding='utf-8') as f:
            json.dump(processed_test, f, ensure_ascii=False, indent=2)

        # 保存标签映射
        with open('/data/TACL_chinese1_reset_v2/processed_data/label_mappings.pkl', 'wb') as f:
            pickle.dump(self.label_mappings, f)

        print("处理后的数据已保存到 processed_data/ 目录")

    def generate_analysis_report(self):
        """生成分析报告"""
        print("生成分析报告...")

        report = {
            'dataset_info': {
                'train_size': len(self.train_data),
                'val_size': len(self.val_data),
                'test_size': len(self.test_data),
                'total_size': len(self.train_data) + len(self.val_data) + len(self.test_data)
            },
            'hierarchy_info': {
                'num_levels': 7,
                'classes_per_level': [self.label_mappings[i]['num_classes'] for i in range(7)],
                'unique_paths': self.hierarchy_stats['unique_paths']
            },
            'label_mappings': self.label_mappings,
            'hierarchy_stats': self.hierarchy_stats
        }

        with open('/data/TACL_chinese1_reset_v2/processed_data/analysis_report.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)

        print("分析报告已保存到 processed_data/analysis_report.json")

    def run_full_analysis(self):
        """运行完整分析"""
        self.load_data()
        self.build_label_mappings()
        self.analyze_hierarchy_structure()
        self.analyze_text_statistics()
        self.create_processed_datasets()
        self.generate_analysis_report()
        print("数据分析完成!")

if __name__ == "__main__":
    analyzer = HierarchicalDataAnalyzer()
    analyzer.run_full_analysis()