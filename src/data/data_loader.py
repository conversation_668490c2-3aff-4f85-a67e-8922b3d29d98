#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据加载器和数据增强
"""

import torch
from torch.utils.data import Dataset, DataLoader
import json
import pickle
import random
import numpy as np
from typing import List, Dict, Tuple
from transformers import AutoTokenizer
import re

class HierarchicalDataset(Dataset):
    """层次化分类数据集 - 增强版，专门优化Level 6分类"""

    def __init__(self, data_path: str, tokenizer, max_length: int = 512,
                 augment: bool = False, augment_prob: float = 0.3,
                 level6_focus: bool = True, level6_augment_factor: int = 3):
        self.tokenizer = tokenizer
        self.max_length = max_length
        self.augment = augment
        self.augment_prob = augment_prob
        self.level6_focus = level6_focus
        self.level6_augment_factor = level6_augment_factor

        # 加载数据
        with open(data_path, 'r', encoding='utf-8') as f:
            self.data = json.load(f)

        # 分析Level 6的类别分布
        if self.level6_focus:
            self._analyze_level6_distribution()
            self._create_level6_enhanced_dataset()

        print(f"加载了 {len(self.data)} 个样本")
        if hasattr(self, 'level6_rare_classes'):
            print(f"Level 6稀有类别数量: {len(self.level6_rare_classes)}")
            print(f"Level 6增强后样本数量: {len(self.enhanced_data) if hasattr(self, 'enhanced_data') else 0}")

    def _analyze_level6_distribution(self):
        """分析Level 6的类别分布，识别稀有类别"""
        from collections import Counter

        level6_labels = [sample['labels'][6] for sample in self.data if len(sample['labels']) > 6]
        self.level6_distribution = Counter(level6_labels)

        # 识别稀有类别（样本数少于平均值的类别）
        avg_samples = len(level6_labels) / len(self.level6_distribution)
        self.level6_rare_classes = [
            label for label, count in self.level6_distribution.items()
            if count < avg_samples * 0.5  # 少于平均值一半的类别
        ]

        print(f"Level 6平均样本数: {avg_samples:.2f}")
        print(f"Level 6稀有类别数量: {len(self.level6_rare_classes)}")

    def _create_level6_enhanced_dataset(self):
        """为Level 6稀有类别创建增强数据集"""
        self.enhanced_data = []

        for sample in self.data:
            if len(sample['labels']) > 6 and sample['labels'][6] in self.level6_rare_classes:
                # 为稀有类别创建多个增强版本
                for _ in range(self.level6_augment_factor):
                    enhanced_sample = sample.copy()
                    enhanced_sample['is_enhanced'] = True
                    self.enhanced_data.append(enhanced_sample)
            else:
                self.enhanced_data.append(sample)

        # 使用增强后的数据集
        self.data = self.enhanced_data

    def __len__(self):
        return len(self.data)

    def __getitem__(self, idx):
        sample = self.data[idx]
        text = sample['text']
        labels = sample['labels']  # List of label indices for each level
        is_enhanced = sample.get('is_enhanced', False)

        # 数据增强 - 对增强样本和Level 6稀有类别使用更强的增强
        should_augment = self.augment and (
            random.random() < self.augment_prob or
            (is_enhanced and random.random() < 0.8) or  # 增强样本80%概率
            (len(labels) > 6 and labels[6] in getattr(self, 'level6_rare_classes', []) and random.random() < 0.6)
        )

        if should_augment:
            text = self._augment_text(text, enhanced=is_enhanced)

        # 分词
        encoded = self.tokenizer(
            text,
            padding='max_length',
            truncation=True,
            max_length=self.max_length,
            return_tensors='pt'
        )

        return {
            'input_ids': encoded['input_ids'].squeeze(),
            'attention_mask': encoded['attention_mask'].squeeze(),
            'labels': torch.tensor(labels, dtype=torch.long),
            'is_level6_rare': len(labels) > 6 and labels[6] in getattr(self, 'level6_rare_classes', [])
        }

    def _augment_text(self, text: str, enhanced: bool = False) -> str:
        """文本数据增强 - 增强版，对Level 6稀有类别使用更强的增强"""
        if enhanced:
            # 对增强样本使用更多的增强方法
            augmentation_methods = [
                self._random_deletion,
                self._random_insertion,
                self._synonym_replacement,
                self._random_swap,
                self._paraphrase_generation,
                self._context_expansion
            ]
            num_methods = random.randint(2, 3)  # 使用更多方法
            selected_methods = random.sample(augmentation_methods, min(num_methods, len(augmentation_methods)))

            augmented_text = text
            for method in selected_methods:
                augmented_text = method(augmented_text)
            return augmented_text
        else:
            augmentation_methods = [
                self._random_deletion,
                self._random_insertion,
                self._synonym_replacement,
                self._random_swap
            ]
            # 随机选择一种增强方法
            method = random.choice(augmentation_methods)
            return method(text)

    def _random_deletion(self, text: str, p: float = 0.1) -> str:
        """随机删除"""
        words = text.split()
        if len(words) == 1:
            return text

        new_words = []
        for word in words:
            if random.random() > p:
                new_words.append(word)

        if len(new_words) == 0:
            return random.choice(words)

        return ' '.join(new_words)

    def _random_insertion(self, text: str, n: int = 1) -> str:
        """随机插入"""
        words = text.split()
        if len(words) < 2:
            return text

        new_words = words.copy()
        for _ in range(n):
            # 随机选择一个词进行插入
            random_word = random.choice(words)
            random_idx = random.randint(0, len(new_words))
            new_words.insert(random_idx, random_word)

        return ' '.join(new_words)

    def _synonym_replacement(self, text: str, n: int = 1) -> str:
        """同义词替换（简化版）"""
        # 这里使用简单的字符替换作为示例
        # 实际应用中可以使用更复杂的同义词词典
        synonyms = {
            '学习': ['学会', '掌握', '了解'],
            '知识': ['内容', '信息', '资料'],
            '方法': ['方式', '途径', '手段'],
            '问题': ['题目', '难题', '疑问'],
            '能力': ['技能', '本领', '水平']
        }

        words = text.split()
        new_words = words.copy()

        for _ in range(n):
            for i, word in enumerate(new_words):
                if word in synonyms and random.random() < 0.5:
                    new_words[i] = random.choice(synonyms[word])

        return ' '.join(new_words)

    def _random_swap(self, text: str, n: int = 1) -> str:
        """随机交换"""
        words = text.split()
        if len(words) < 2:
            return text

        new_words = words.copy()
        for _ in range(n):
            idx1, idx2 = random.sample(range(len(new_words)), 2)
            new_words[idx1], new_words[idx2] = new_words[idx2], new_words[idx1]

        return ' '.join(new_words)

    def _paraphrase_generation(self, text: str) -> str:
        """释义生成 - 简单的句式变换"""
        # 简单的句式变换规则
        patterns = [
            (r'(.+)是(.+)', r'\2就是\1'),
            (r'(.+)可以(.+)', r'\1能够\2'),
            (r'(.+)应该(.+)', r'\1需要\2'),
            (r'(.+)包括(.+)', r'\1含有\2'),
        ]

        for pattern, replacement in patterns:
            if re.search(pattern, text):
                text = re.sub(pattern, replacement, text, count=1)
                break

        return text

    def _context_expansion(self, text: str) -> str:
        """上下文扩展 - 添加教育相关的上下文词汇"""
        education_contexts = [
            '在小学语文学习中，',
            '根据教学要求，',
            '从知识点角度看，',
            '在语文教育实践中，',
            '按照课程标准，'
        ]

        if random.random() < 0.3:  # 30%概率添加前缀
            prefix = random.choice(education_contexts)
            text = prefix + text

        return text

class Level6FocusedSampler:
    """专门针对Level 6的采样器，确保稀有类别得到充分训练"""

    def __init__(self, dataset, batch_size: int):
        self.dataset = dataset
        self.batch_size = batch_size

        # 分析Level 6的分布
        self.level6_indices = {}
        self.rare_indices = []
        self.common_indices = []

        for idx, sample in enumerate(dataset.data):
            if len(sample['labels']) > 6:
                level6_label = sample['labels'][6]
                if level6_label not in self.level6_indices:
                    self.level6_indices[level6_label] = []
                self.level6_indices[level6_label].append(idx)

                # 判断是否为稀有类别
                if hasattr(dataset, 'level6_rare_classes') and level6_label in dataset.level6_rare_classes:
                    self.rare_indices.append(idx)
                else:
                    self.common_indices.append(idx)

        print(f"Level 6稀有样本数量: {len(self.rare_indices)}")
        print(f"Level 6常见样本数量: {len(self.common_indices)}")

    def __iter__(self):
        # 确保每个batch中都有一定比例的稀有类别样本
        rare_ratio = 0.4  # 40%的样本来自稀有类别
        rare_per_batch = int(self.batch_size * rare_ratio)
        common_per_batch = self.batch_size - rare_per_batch

        # 计算需要的batch数量
        total_samples = len(self.dataset)
        num_batches = (total_samples + self.batch_size - 1) // self.batch_size

        for _ in range(num_batches):
            batch_indices = []

            # 添加稀有类别样本
            if len(self.rare_indices) > 0:
                rare_samples = random.choices(self.rare_indices, k=rare_per_batch)
                batch_indices.extend(rare_samples)

            # 添加常见类别样本
            if len(self.common_indices) > 0:
                common_samples = random.choices(self.common_indices, k=common_per_batch)
                batch_indices.extend(common_samples)

            # 如果样本不足，用随机样本补充
            while len(batch_indices) < self.batch_size:
                batch_indices.append(random.randint(0, len(self.dataset) - 1))

            yield batch_indices[:self.batch_size]

    def __len__(self):
        return (len(self.dataset) + self.batch_size - 1) // self.batch_size

class BalancedBatchSampler:
    """平衡批次采样器，用于处理类别不平衡问题"""

    def __init__(self, dataset, batch_size: int, level: int = 0):
        self.dataset = dataset
        self.batch_size = batch_size
        self.level = level

        # 按指定层级的标签分组样本
        self.label_to_indices = {}
        for idx, sample in enumerate(dataset.data):
            label = sample['labels'][level]
            if label not in self.label_to_indices:
                self.label_to_indices[label] = []
            self.label_to_indices[label].append(idx)

        self.labels = list(self.label_to_indices.keys())
        self.num_classes = len(self.labels)

    def __iter__(self):
        # 计算每个批次中每个类别的样本数
        samples_per_class = max(1, self.batch_size // self.num_classes)

        while True:
            batch_indices = []

            for label in self.labels:
                indices = self.label_to_indices[label]
                selected = random.choices(indices, k=samples_per_class)
                batch_indices.extend(selected)

            # 如果批次大小不够，随机补充
            while len(batch_indices) < self.batch_size:
                random_label = random.choice(self.labels)
                random_idx = random.choice(self.label_to_indices[random_label])
                batch_indices.append(random_idx)

            # 随机打乱批次内的顺序
            random.shuffle(batch_indices)
            yield batch_indices[:self.batch_size]

    def __len__(self):
        return len(self.dataset) // self.batch_size

def create_data_loaders(train_path: str, val_path: str, test_path: str,
                       tokenizer, batch_size: int = 32, max_length: int = 512,
                       num_workers: int = 4, use_balanced_sampling: bool = True,
                       use_level6_focus: bool = True) -> Tuple[DataLoader, DataLoader, DataLoader]:
    """创建数据加载器 - 增强版，专门优化Level 6"""

    # 创建数据集 - 启用Level 6专门优化
    train_dataset = HierarchicalDataset(
        train_path, tokenizer, max_length, augment=True,
        level6_focus=use_level6_focus, level6_augment_factor=3
    )
    val_dataset = HierarchicalDataset(
        val_path, tokenizer, max_length, augment=False,
        level6_focus=False  # 验证集不需要增强
    )
    test_dataset = HierarchicalDataset(
        test_path, tokenizer, max_length, augment=False,
        level6_focus=False  # 测试集不需要增强
    )

    # 创建数据加载器
    if use_balanced_sampling and use_level6_focus:
        # 使用Level 6专门的采样器
        train_sampler = Level6FocusedSampler(train_dataset, batch_size)
        train_loader = DataLoader(
            train_dataset,
            batch_sampler=train_sampler,
            num_workers=num_workers,
            pin_memory=True
        )
    elif use_balanced_sampling:
        # 使用传统的平衡采样
        train_sampler = BalancedBatchSampler(train_dataset, batch_size)
        train_loader = DataLoader(
            train_dataset,
            batch_sampler=train_sampler,
            num_workers=num_workers,
            pin_memory=True
        )
    else:
        train_loader = DataLoader(
            train_dataset,
            batch_size=batch_size,
            shuffle=True,
            num_workers=num_workers,
            pin_memory=True,
            drop_last=True  # 避免batch size不一致
        )

    val_loader = DataLoader(
        val_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=True,
        drop_last=True  # 避免batch size不一致
    )

    test_loader = DataLoader(
        test_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=True,
        drop_last=True  # 避免batch size不一致
    )

    return train_loader, val_loader, test_loader