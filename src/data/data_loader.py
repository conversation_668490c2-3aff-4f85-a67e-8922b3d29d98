#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据加载器和数据增强
"""

import torch
from torch.utils.data import Dataset, DataLoader
import json
import pickle
import random
import numpy as np
from typing import List, Dict, Tuple
from transformers import AutoTokenizer
import re

class HierarchicalDataset(Dataset):
    """层次化分类数据集"""

    def __init__(self, data_path: str, tokenizer, max_length: int = 512,
                 augment: bool = False, augment_prob: float = 0.3):
        self.tokenizer = tokenizer
        self.max_length = max_length
        self.augment = augment
        self.augment_prob = augment_prob

        # 加载数据
        with open(data_path, 'r', encoding='utf-8') as f:
            self.data = json.load(f)

        print(f"加载了 {len(self.data)} 个样本")

    def __len__(self):
        return len(self.data)

    def __getitem__(self, idx):
        sample = self.data[idx]
        text = sample['text']
        labels = sample['labels']  # List of label indices for each level

        # 数据增强
        if self.augment and random.random() < self.augment_prob:
            text = self._augment_text(text)

        # 分词
        encoded = self.tokenizer(
            text,
            padding='max_length',
            truncation=True,
            max_length=self.max_length,
            return_tensors='pt'
        )

        return {
            'input_ids': encoded['input_ids'].squeeze(),
            'attention_mask': encoded['attention_mask'].squeeze(),
            'labels': torch.tensor(labels, dtype=torch.long)
        }

    def _augment_text(self, text: str) -> str:
        """文本数据增强"""
        augmentation_methods = [
            self._random_deletion,
            self._random_insertion,
            self._synonym_replacement,
            self._random_swap
        ]

        # 随机选择一种增强方法
        method = random.choice(augmentation_methods)
        return method(text)

    def _random_deletion(self, text: str, p: float = 0.1) -> str:
        """随机删除"""
        words = text.split()
        if len(words) == 1:
            return text

        new_words = []
        for word in words:
            if random.random() > p:
                new_words.append(word)

        if len(new_words) == 0:
            return random.choice(words)

        return ' '.join(new_words)

    def _random_insertion(self, text: str, n: int = 1) -> str:
        """随机插入"""
        words = text.split()
        if len(words) < 2:
            return text

        new_words = words.copy()
        for _ in range(n):
            # 随机选择一个词进行插入
            random_word = random.choice(words)
            random_idx = random.randint(0, len(new_words))
            new_words.insert(random_idx, random_word)

        return ' '.join(new_words)

    def _synonym_replacement(self, text: str, n: int = 1) -> str:
        """同义词替换（简化版）"""
        # 这里使用简单的字符替换作为示例
        # 实际应用中可以使用更复杂的同义词词典
        synonyms = {
            '学习': ['学会', '掌握', '了解'],
            '知识': ['内容', '信息', '资料'],
            '方法': ['方式', '途径', '手段'],
            '问题': ['题目', '难题', '疑问'],
            '能力': ['技能', '本领', '水平']
        }

        words = text.split()
        new_words = words.copy()

        for _ in range(n):
            for i, word in enumerate(new_words):
                if word in synonyms and random.random() < 0.5:
                    new_words[i] = random.choice(synonyms[word])

        return ' '.join(new_words)

    def _random_swap(self, text: str, n: int = 1) -> str:
        """随机交换"""
        words = text.split()
        if len(words) < 2:
            return text

        new_words = words.copy()
        for _ in range(n):
            idx1, idx2 = random.sample(range(len(new_words)), 2)
            new_words[idx1], new_words[idx2] = new_words[idx2], new_words[idx1]

        return ' '.join(new_words)

class BalancedBatchSampler:
    """平衡批次采样器，用于处理类别不平衡问题"""

    def __init__(self, dataset, batch_size: int, level: int = 0):
        self.dataset = dataset
        self.batch_size = batch_size
        self.level = level

        # 按指定层级的标签分组样本
        self.label_to_indices = {}
        for idx, sample in enumerate(dataset.data):
            label = sample['labels'][level]
            if label not in self.label_to_indices:
                self.label_to_indices[label] = []
            self.label_to_indices[label].append(idx)

        self.labels = list(self.label_to_indices.keys())
        self.num_classes = len(self.labels)

    def __iter__(self):
        # 计算每个批次中每个类别的样本数
        samples_per_class = max(1, self.batch_size // self.num_classes)

        while True:
            batch_indices = []

            for label in self.labels:
                indices = self.label_to_indices[label]
                selected = random.choices(indices, k=samples_per_class)
                batch_indices.extend(selected)

            # 如果批次大小不够，随机补充
            while len(batch_indices) < self.batch_size:
                random_label = random.choice(self.labels)
                random_idx = random.choice(self.label_to_indices[random_label])
                batch_indices.append(random_idx)

            # 随机打乱批次内的顺序
            random.shuffle(batch_indices)
            yield batch_indices[:self.batch_size]

    def __len__(self):
        return len(self.dataset) // self.batch_size

def create_data_loaders(train_path: str, val_path: str, test_path: str,
                       tokenizer, batch_size: int = 32, max_length: int = 512,
                       num_workers: int = 4, use_balanced_sampling: bool = True) -> Tuple[DataLoader, DataLoader, DataLoader]:
    """创建数据加载器"""

    # 创建数据集
    train_dataset = HierarchicalDataset(
        train_path, tokenizer, max_length, augment=True
    )
    val_dataset = HierarchicalDataset(
        val_path, tokenizer, max_length, augment=False
    )
    test_dataset = HierarchicalDataset(
        test_path, tokenizer, max_length, augment=False
    )

    # 创建数据加载器
    if use_balanced_sampling:
        # 使用平衡采样
        train_sampler = BalancedBatchSampler(train_dataset, batch_size)
        train_loader = DataLoader(
            train_dataset,
            batch_sampler=train_sampler,
            num_workers=num_workers,
            pin_memory=True
        )
    else:
        train_loader = DataLoader(
            train_dataset,
            batch_size=batch_size,
            shuffle=True,
            num_workers=num_workers,
            pin_memory=True,
            drop_last=True  # 避免batch size不一致
        )

    val_loader = DataLoader(
        val_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=True,
        drop_last=True  # 避免batch size不一致
    )

    test_loader = DataLoader(
        test_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=True,
        drop_last=True  # 避免batch size不一致
    )

    return train_loader, val_loader, test_loader