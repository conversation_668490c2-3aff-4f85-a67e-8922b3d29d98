#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于Qwen3-0.6B的层次化分类模型
实现多任务学习和层次约束的高级架构
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from transformers import AutoModel, AutoTokenizer, AutoConfig
import numpy as np
import json
import pickle
from typing import Dict, List, Tuple, Optional

class HierarchicalAttention(nn.Module):
    """层次化注意力机制"""
    def __init__(self, hidden_size: int, num_levels: int):
        super().__init__()
        self.hidden_size = hidden_size
        self.num_levels = num_levels

        # 为每一层创建注意力权重
        self.level_attentions = nn.ModuleList([
            nn.Linear(hidden_size, hidden_size) for _ in range(num_levels)
        ])

        # 层次间的交互注意力
        self.cross_level_attention = nn.MultiheadAttention(
            embed_dim=hidden_size,
            num_heads=8,
            dropout=0.1,
            batch_first=True
        )

    def forward(self, hidden_states: torch.Tensor, level_embeddings: List[torch.Tensor]) -> torch.Tensor:
        """
        Args:
            hidden_states: [batch_size, seq_len, hidden_size]
            level_embeddings: List of [batch_size, hidden_size] for each level
        """
        batch_size = hidden_states.size(0)

        # 计算每层的注意力权重
        level_contexts = []
        for i, level_emb in enumerate(level_embeddings):
            # 扩展level_emb到序列长度
            level_emb_expanded = level_emb.unsqueeze(1).expand(-1, hidden_states.size(1), -1)

            # 计算注意力分数
            attention_scores = torch.bmm(
                self.level_attentions[i](level_emb_expanded),
                hidden_states.transpose(1, 2)
            )  # [batch_size, seq_len, seq_len]

            attention_weights = F.softmax(attention_scores, dim=-1)
            context = torch.bmm(attention_weights, hidden_states)  # [batch_size, seq_len, hidden_size]
            level_contexts.append(context.mean(dim=1))  # [batch_size, hidden_size]

        # 层次间交互注意力
        level_stack = torch.stack(level_contexts, dim=1)  # [batch_size, num_levels, hidden_size]
        attended_levels, _ = self.cross_level_attention(level_stack, level_stack, level_stack)

        return attended_levels  # [batch_size, num_levels, hidden_size]

class HierarchicalClassifier(nn.Module):
    """层次化分类器"""
    def __init__(self, hidden_size: int, num_classes_per_level: List[int], dropout: float = 0.1):
        super().__init__()
        self.num_levels = len(num_classes_per_level)
        self.num_classes_per_level = num_classes_per_level

        # 为每一层创建分类头
        self.classifiers = nn.ModuleList()
        for i, num_classes in enumerate(num_classes_per_level):
            # 使用前面层的信息来增强当前层的分类
            input_size = hidden_size + sum(num_classes_per_level[:i])

            classifier = nn.Sequential(
                nn.Linear(input_size, hidden_size),
                nn.LayerNorm(hidden_size),
                nn.ReLU(),
                nn.Dropout(dropout),
                nn.Linear(hidden_size, hidden_size // 2),
                nn.LayerNorm(hidden_size // 2),
                nn.ReLU(),
                nn.Dropout(dropout),
                nn.Linear(hidden_size // 2, num_classes)
            )
            self.classifiers.append(classifier)

    def forward(self, level_features: torch.Tensor) -> List[torch.Tensor]:
        """
        Args:
            level_features: [batch_size, num_levels, hidden_size]
        Returns:
            List of logits for each level
        """
        outputs = []
        cumulative_probs = []

        for i in range(self.num_levels):
            # 当前层的特征
            current_feature = level_features[:, i, :]  # [batch_size, hidden_size]

            # 结合前面层的概率分布作为额外特征
            if cumulative_probs:
                combined_probs = torch.cat(cumulative_probs, dim=1)
                current_input = torch.cat([current_feature, combined_probs], dim=1)
            else:
                current_input = current_feature

            # 分类
            logits = self.classifiers[i](current_input)
            outputs.append(logits)

            # 保存当前层的概率分布用于下一层
            probs = F.softmax(logits, dim=-1)
            cumulative_probs.append(probs)

        return outputs

class HierarchicalQwenModel(nn.Module):
    """基于Qwen3-0.6B的层次化分类模型"""
    def __init__(self, model_path: str, num_classes_per_level: List[int],
                 max_length: int = 512, dropout: float = 0.1):
        super().__init__()

        # 加载预训练模型
        self.config = AutoConfig.from_pretrained(model_path)
        self.backbone = AutoModel.from_pretrained(model_path)
        self.tokenizer = AutoTokenizer.from_pretrained(model_path)

        # 设置特殊token
        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token

        self.hidden_size = self.config.hidden_size
        self.max_length = max_length
        self.num_levels = len(num_classes_per_level)
        self.num_classes_per_level = num_classes_per_level

        # 层次化注意力机制
        self.hierarchical_attention = HierarchicalAttention(
            hidden_size=self.hidden_size,
            num_levels=self.num_levels
        )

        # 层次化分类器
        self.hierarchical_classifier = HierarchicalClassifier(
            hidden_size=self.hidden_size,
            num_classes_per_level=num_classes_per_level,
            dropout=dropout
        )

        # 层次一致性约束
        self.consistency_weight = nn.Parameter(torch.ones(self.num_levels))

        # 初始化层级嵌入
        self.level_embeddings = nn.ModuleList([
            nn.Linear(self.hidden_size, self.hidden_size) for _ in range(self.num_levels)
        ])

    def forward(self, input_ids: torch.Tensor, attention_mask: torch.Tensor,
                labels: Optional[List[torch.Tensor]] = None) -> Dict[str, torch.Tensor]:
        """
        Args:
            input_ids: [batch_size, seq_len]
            attention_mask: [batch_size, seq_len]
            labels: List of [batch_size] for each level
        """
        # 获取backbone输出
        outputs = self.backbone(input_ids=input_ids, attention_mask=attention_mask)
        hidden_states = outputs.last_hidden_state  # [batch_size, seq_len, hidden_size]
        pooled_output = outputs.pooler_output if hasattr(outputs, 'pooler_output') else hidden_states.mean(dim=1)

        # 生成层级嵌入
        level_embeddings = []
        for i, level_emb_layer in enumerate(self.level_embeddings):
            level_emb = level_emb_layer(pooled_output)  # [batch_size, hidden_size]
            level_embeddings.append(level_emb)

        # 层次化注意力
        level_features = self.hierarchical_attention(hidden_states, level_embeddings)

        # 层次化分类
        logits_list = self.hierarchical_classifier(level_features)

        result = {
            'logits': logits_list,
            'level_features': level_features
        }

        if labels is not None:
            # 计算损失
            total_loss = 0
            level_losses = []

            for i, (logits, label) in enumerate(zip(logits_list, labels)):
                # 基础交叉熵损失
                ce_loss = F.cross_entropy(logits, label)
                level_losses.append(ce_loss)

                # 加权损失（深层权重更大）
                weight = self.consistency_weight[i] * (i + 1)
                total_loss += weight * ce_loss

            # 层次一致性损失
            consistency_loss = self._compute_consistency_loss(logits_list, labels)
            total_loss += 0.1 * consistency_loss

            result.update({
                'loss': total_loss,
                'level_losses': level_losses,
                'consistency_loss': consistency_loss
            })

        return result

    def _compute_consistency_loss(self, logits_list: List[torch.Tensor],
                                labels: List[torch.Tensor]) -> torch.Tensor:
        """计算层次一致性损失"""
        if len(logits_list) <= 1:
            return torch.tensor(0.0, device=logits_list[0].device)

        consistency_loss = torch.tensor(0.0, device=logits_list[0].device)

        for i in range(1, len(logits_list)):
            # 当前层的预测概率
            current_probs = F.softmax(logits_list[i], dim=-1)

            # 简化的一致性损失：使用温度缩放的软目标
            temperature = 3.0
            soft_targets = F.softmax(logits_list[i-1] / temperature, dim=-1)

            # 如果类别数不同，使用简单的KL散度
            # 不进行复杂的形状变换，而是直接计算分布间的差异
            if current_probs.size(-1) != soft_targets.size(-1):
                # 使用简单的L2距离作为一致性约束
                # 将概率分布映射到相同维度
                min_dim = min(current_probs.size(-1), soft_targets.size(-1))
                current_truncated = current_probs[:, :min_dim]
                parent_truncated = soft_targets[:, :min_dim]

                # 归一化
                current_truncated = current_truncated / (current_truncated.sum(dim=-1, keepdim=True) + 1e-8)
                parent_truncated = parent_truncated / (parent_truncated.sum(dim=-1, keepdim=True) + 1e-8)

                kl_loss = F.mse_loss(current_truncated, parent_truncated)
            else:
                # 相同维度时使用KL散度
                kl_loss = F.kl_div(
                    current_probs.log(),
                    soft_targets,
                    reduction='batchmean'
                )

            consistency_loss = consistency_loss + kl_loss

        return consistency_loss

    def predict(self, texts: List[str], batch_size: int = 32) -> List[List[int]]:
        """批量预测"""
        self.eval()
        predictions = []

        with torch.no_grad():
            for i in range(0, len(texts), batch_size):
                batch_texts = texts[i:i+batch_size]

                # 分词
                encoded = self.tokenizer(
                    batch_texts,
                    padding=True,
                    truncation=True,
                    max_length=self.max_length,
                    return_tensors='pt'
                )

                # 移动到设备
                input_ids = encoded['input_ids'].to(next(self.parameters()).device)
                attention_mask = encoded['attention_mask'].to(next(self.parameters()).device)

                # 前向传播
                outputs = self.forward(input_ids, attention_mask)
                logits_list = outputs['logits']

                # 获取预测结果
                batch_predictions = []
                for j in range(len(batch_texts)):
                    sample_predictions = []
                    for level_logits in logits_list:
                        pred = torch.argmax(level_logits[j], dim=-1).item()
                        sample_predictions.append(pred)
                    batch_predictions.append(sample_predictions)

                predictions.extend(batch_predictions)

        return predictions