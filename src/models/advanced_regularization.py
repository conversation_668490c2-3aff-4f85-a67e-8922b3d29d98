#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级正则化技术 - 专门为Level 6优化设计
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import List, Dict, Optional


class DropBlock2D(nn.Module):
    """DropBlock正则化 - 比标准Dropout更有效"""
    
    def __init__(self, drop_rate: float = 0.1, block_size: int = 7):
        super().__init__()
        self.drop_rate = drop_rate
        self.block_size = block_size
    
    def forward(self, x):
        if not self.training:
            return x
        
        # x shape: [batch_size, channels, height, width] or [batch_size, seq_len, hidden_size]
        if len(x.shape) == 3:  # 文本序列
            batch_size, seq_len, hidden_size = x.shape
            # 将序列维度视为"空间"维度
            x = x.view(batch_size, 1, seq_len, hidden_size)
            
        batch_size, channels, height, width = x.shape
        
        # 计算gamma
        gamma = self.drop_rate / (self.block_size ** 2)
        
        # 生成mask
        mask = torch.bernoulli(torch.full((batch_size, channels, height, width), gamma, device=x.device))
        
        # 应用block结构
        mask = F.max_pool2d(mask, kernel_size=self.block_size, stride=1, 
                           padding=self.block_size // 2)
        
        # 归一化
        mask = 1 - mask
        normalize_factor = mask.numel() / mask.sum()
        
        if len(x.shape) == 4 and x.shape[1] == 1:  # 恢复文本序列形状
            mask = mask.view(batch_size, seq_len, hidden_size)
            x = x.view(batch_size, seq_len, hidden_size)
        
        return x * mask * normalize_factor


class LabelSmoothingLoss(nn.Module):
    """标签平滑损失 - 提升泛化能力"""
    
    def __init__(self, num_classes: int, smoothing: float = 0.1):
        super().__init__()
        self.num_classes = num_classes
        self.smoothing = smoothing
        self.confidence = 1.0 - smoothing
    
    def forward(self, pred, target):
        """
        pred: [batch_size, num_classes]
        target: [batch_size]
        """
        pred = F.log_softmax(pred, dim=-1)
        
        # 创建平滑标签
        true_dist = torch.zeros_like(pred)
        true_dist.fill_(self.smoothing / (self.num_classes - 1))
        true_dist.scatter_(1, target.unsqueeze(1), self.confidence)
        
        return torch.mean(torch.sum(-true_dist * pred, dim=-1))


class MixUp:
    """MixUp数据增强"""
    
    def __init__(self, alpha: float = 0.2):
        self.alpha = alpha
    
    def __call__(self, x, y):
        if self.alpha > 0:
            lam = np.random.beta(self.alpha, self.alpha)
        else:
            lam = 1
        
        batch_size = x.size(0)
        index = torch.randperm(batch_size).to(x.device)
        
        mixed_x = lam * x + (1 - lam) * x[index, :]
        y_a, y_b = y, y[index]
        
        return mixed_x, y_a, y_b, lam


class CutMix:
    """CutMix数据增强 - 适配文本序列"""
    
    def __init__(self, alpha: float = 1.0):
        self.alpha = alpha
    
    def __call__(self, x, y):
        if self.alpha > 0:
            lam = np.random.beta(self.alpha, self.alpha)
        else:
            lam = 1
        
        batch_size = x.size(0)
        index = torch.randperm(batch_size).to(x.device)
        
        # 对于文本序列，随机选择一段进行替换
        seq_len = x.size(1)
        cut_len = int(seq_len * (1 - lam))
        cut_start = np.random.randint(0, seq_len - cut_len + 1)
        cut_end = cut_start + cut_len
        
        mixed_x = x.clone()
        mixed_x[:, cut_start:cut_end] = x[index, cut_start:cut_end]
        
        y_a, y_b = y, y[index]
        
        return mixed_x, y_a, y_b, lam


class TemperatureScaling(nn.Module):
    """温度缩放 - 校准预测概率"""
    
    def __init__(self):
        super().__init__()
        self.temperature = nn.Parameter(torch.ones(1))
    
    def forward(self, logits):
        return logits / self.temperature


class Level6EnhancedRegularizer(nn.Module):
    """Level 6专门的正则化模块"""
    
    def __init__(self, hidden_size: int, num_classes: int, 
                 use_dropblock: bool = True, use_label_smoothing: bool = True,
                 smoothing: float = 0.1):
        super().__init__()
        self.hidden_size = hidden_size
        self.num_classes = num_classes
        
        # DropBlock正则化
        if use_dropblock:
            self.dropblock = DropBlock2D(drop_rate=0.1, block_size=3)
        else:
            self.dropblock = None
        
        # 标签平滑
        if use_label_smoothing:
            self.label_smoothing = LabelSmoothingLoss(num_classes, smoothing)
        else:
            self.label_smoothing = None
        
        # 温度缩放
        self.temperature_scaling = TemperatureScaling()
        
        # 特征噪声注入
        self.feature_noise = nn.Dropout(0.05)
        
        # 对比学习组件
        self.contrastive_head = nn.Sequential(
            nn.Linear(hidden_size, hidden_size // 2),
            nn.ReLU(),
            nn.Linear(hidden_size // 2, 128)  # 对比学习特征维度
        )
    
    def forward(self, features, logits, targets=None, training=True):
        """
        features: [batch_size, hidden_size]
        logits: [batch_size, num_classes]
        targets: [batch_size] (可选)
        """
        regularized_features = features
        
        if training:
            # 应用特征噪声
            regularized_features = self.feature_noise(regularized_features)
            
            # 应用DropBlock
            if self.dropblock is not None:
                regularized_features = self.dropblock(regularized_features.unsqueeze(1)).squeeze(1)
        
        # 温度缩放
        calibrated_logits = self.temperature_scaling(logits)
        
        # 计算损失
        losses = {}
        
        if targets is not None and training:
            # 标签平滑损失
            if self.label_smoothing is not None:
                losses['label_smoothing'] = self.label_smoothing(calibrated_logits, targets)
            
            # 对比学习损失
            contrastive_features = self.contrastive_head(regularized_features)
            losses['contrastive'] = self._compute_contrastive_loss(contrastive_features, targets)
        
        return {
            'regularized_features': regularized_features,
            'calibrated_logits': calibrated_logits,
            'losses': losses
        }
    
    def _compute_contrastive_loss(self, features, targets, temperature=0.1):
        """计算对比学习损失"""
        # 归一化特征
        features = F.normalize(features, dim=1)
        
        # 计算相似度矩阵
        similarity_matrix = torch.matmul(features, features.T) / temperature
        
        # 创建正样本mask
        batch_size = features.size(0)
        mask = torch.eq(targets.unsqueeze(0), targets.unsqueeze(1)).float()
        mask = mask - torch.eye(batch_size, device=features.device)  # 排除自己
        
        # 计算对比损失
        exp_sim = torch.exp(similarity_matrix)
        sum_exp_sim = torch.sum(exp_sim, dim=1, keepdim=True)
        
        log_prob = similarity_matrix - torch.log(sum_exp_sim)
        mean_log_prob_pos = torch.sum(mask * log_prob, dim=1) / torch.sum(mask, dim=1).clamp(min=1)
        
        loss = -mean_log_prob_pos.mean()
        return loss


class EnsemblePredictor:
    """模型集成预测器"""
    
    def __init__(self, models: List[nn.Module], weights: Optional[List[float]] = None):
        self.models = models
        self.weights = weights or [1.0 / len(models)] * len(models)
    
    def predict(self, x):
        """集成预测"""
        predictions = []
        
        for model in self.models:
            model.eval()
            with torch.no_grad():
                pred = model(x)
                if isinstance(pred, dict):
                    pred = pred['logits']
                predictions.append(pred)
        
        # 加权平均
        ensemble_pred = torch.zeros_like(predictions[0])
        for pred, weight in zip(predictions, self.weights):
            ensemble_pred += weight * pred
        
        return ensemble_pred
    
    def predict_with_uncertainty(self, x):
        """预测并估计不确定性"""
        predictions = []
        
        for model in self.models:
            model.eval()
            with torch.no_grad():
                pred = model(x)
                if isinstance(pred, dict):
                    pred = pred['logits']
                predictions.append(F.softmax(pred, dim=-1))
        
        # 计算平均预测和不确定性
        stacked_preds = torch.stack(predictions, dim=0)
        mean_pred = torch.mean(stacked_preds, dim=0)
        uncertainty = torch.var(stacked_preds, dim=0).mean(dim=-1)
        
        return mean_pred, uncertainty
