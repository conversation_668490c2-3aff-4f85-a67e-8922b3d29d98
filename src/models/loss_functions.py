#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级损失函数设计
包括焦点损失、标签平滑、层次一致性损失等
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import List, Dict, Optional

class FocalLoss(nn.Module):
    """焦点损失，用于处理类别不平衡问题"""

    def __init__(self, alpha: Optional[torch.Tensor] = None, gamma: float = 2.0,
                 reduction: str = 'mean'):
        super().__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.reduction = reduction

    def forward(self, inputs: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        """
        Args:
            inputs: [batch_size, num_classes] 预测logits
            targets: [batch_size] 真实标签
        """
        ce_loss = F.cross_entropy(inputs, targets, reduction='none')
        pt = torch.exp(-ce_loss)
        focal_loss = (1 - pt) ** self.gamma * ce_loss

        if self.alpha is not None:
            if self.alpha.device != inputs.device:
                self.alpha = self.alpha.to(inputs.device)
            alpha_t = self.alpha[targets]
            focal_loss = alpha_t * focal_loss

        if self.reduction == 'mean':
            return focal_loss.mean()
        elif self.reduction == 'sum':
            return focal_loss.sum()
        else:
            return focal_loss

class LabelSmoothingLoss(nn.Module):
    """标签平滑损失"""

    def __init__(self, num_classes: int, smoothing: float = 0.1):
        super().__init__()
        self.num_classes = num_classes
        self.smoothing = smoothing
        self.confidence = 1.0 - smoothing

    def forward(self, inputs: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        """
        Args:
            inputs: [batch_size, num_classes] 预测logits
            targets: [batch_size] 真实标签
        """
        # 如果只有一个类别，直接返回交叉熵损失
        if self.num_classes <= 1:
            return F.cross_entropy(inputs, targets)

        log_probs = F.log_softmax(inputs, dim=-1)

        # 创建平滑标签
        smooth_targets = torch.zeros_like(log_probs)
        smooth_targets.fill_(self.smoothing / (self.num_classes - 1))
        smooth_targets.scatter_(1, targets.unsqueeze(1), self.confidence)

        loss = -torch.sum(smooth_targets * log_probs, dim=-1)
        return loss.mean()

class HierarchicalConsistencyLoss(nn.Module):
    """层次一致性损失"""

    def __init__(self, hierarchy_matrix: torch.Tensor, lambda_consistency: float = 0.1):
        super().__init__()
        self.hierarchy_matrix = hierarchy_matrix  # [num_levels, max_classes, max_classes]
        self.lambda_consistency = lambda_consistency

    def forward(self, logits_list: List[torch.Tensor],
                targets_list: List[torch.Tensor]) -> torch.Tensor:
        """
        Args:
            logits_list: List of [batch_size, num_classes_i] for each level
            targets_list: List of [batch_size] for each level
        """
        consistency_loss = 0

        for i in range(1, len(logits_list)):
            parent_probs = F.softmax(logits_list[i-1], dim=-1)
            child_probs = F.softmax(logits_list[i], dim=-1)

            # 使用层次矩阵计算期望的子类概率
            if i-1 < self.hierarchy_matrix.size(0):
                hierarchy_map = self.hierarchy_matrix[i-1].to(child_probs.device)
                expected_child_probs = torch.matmul(parent_probs, hierarchy_map)

                # 计算KL散度
                kl_loss = F.kl_div(
                    child_probs.log(),
                    expected_child_probs,
                    reduction='batchmean'
                )
                consistency_loss += kl_loss

        return self.lambda_consistency * consistency_loss

class ContrastiveLoss(nn.Module):
    """对比学习损失"""

    def __init__(self, temperature: float = 0.07):
        super().__init__()
        self.temperature = temperature

    def forward(self, features: torch.Tensor, labels: torch.Tensor) -> torch.Tensor:
        """
        Args:
            features: [batch_size, feature_dim] 特征向量
            labels: [batch_size] 标签
        """
        batch_size = features.size(0)

        # 归一化特征
        features = F.normalize(features, dim=1)

        # 计算相似度矩阵
        similarity_matrix = torch.matmul(features, features.T) / self.temperature

        # 创建标签掩码
        labels = labels.unsqueeze(1)
        mask = torch.eq(labels, labels.T).float()

        # 移除对角线（自己与自己的相似度）
        mask = mask - torch.eye(batch_size, device=mask.device)

        # 计算对比损失
        exp_sim = torch.exp(similarity_matrix)
        sum_exp_sim = torch.sum(exp_sim, dim=1, keepdim=True)

        log_prob = similarity_matrix - torch.log(sum_exp_sim)
        mean_log_prob_pos = torch.sum(mask * log_prob, dim=1) / torch.sum(mask, dim=1)

        loss = -mean_log_prob_pos
        return loss.mean()

class HierarchicalLossFunction(nn.Module):
    """综合层次化损失函数"""

    def __init__(self, num_classes_per_level: List[int],
                 class_weights: Optional[List[torch.Tensor]] = None,
                 focal_gamma: float = 2.0,
                 label_smoothing: float = 0.1,
                 consistency_weight: float = 0.1,
                 contrastive_weight: float = 0.05,
                 level_weights: Optional[List[float]] = None):
        super().__init__()

        self.num_levels = len(num_classes_per_level)
        self.num_classes_per_level = num_classes_per_level

        # 为每一层创建损失函数
        self.focal_losses = nn.ModuleList()
        self.label_smoothing_losses = nn.ModuleList()

        for i, num_classes in enumerate(num_classes_per_level):
            # 焦点损失
            alpha = class_weights[i] if class_weights else None
            focal_loss = FocalLoss(alpha=alpha, gamma=focal_gamma)
            self.focal_losses.append(focal_loss)

            # 标签平滑损失
            ls_loss = LabelSmoothingLoss(num_classes, smoothing=label_smoothing)
            self.label_smoothing_losses.append(ls_loss)

        # 对比学习损失
        self.contrastive_loss = ContrastiveLoss()

        # 权重设置
        self.level_weights = level_weights if level_weights else [1.0] * self.num_levels
        self.consistency_weight = consistency_weight
        self.contrastive_weight = contrastive_weight

    def forward(self, outputs: Dict[str, torch.Tensor],
                targets: List[torch.Tensor]) -> Dict[str, torch.Tensor]:
        """
        Args:
            outputs: 模型输出，包含 'logits', 'level_features' 等
            targets: List of [batch_size] for each level
        """
        logits_list = outputs['logits']
        level_features = outputs['level_features']

        total_loss = 0
        level_losses = []

        # 计算每层的损失
        for i, (logits, target) in enumerate(zip(logits_list, targets)):
            # 焦点损失
            focal_loss = self.focal_losses[i](logits, target)

            # 标签平滑损失
            ls_loss = self.label_smoothing_losses[i](logits, target)

            # 组合损失
            level_loss = 0.7 * focal_loss + 0.3 * ls_loss
            level_losses.append(level_loss)

            # 加权累加
            total_loss += self.level_weights[i] * level_loss

        # 层次一致性损失
        consistency_loss = self._compute_consistency_loss(logits_list, targets)
        total_loss += self.consistency_weight * consistency_loss

        # 对比学习损失（使用最后一层的特征）
        if level_features.size(1) > 0:
            last_level_features = level_features[:, -1, :]  # [batch_size, hidden_size]
            contrastive_loss = self.contrastive_loss(last_level_features, targets[-1])
            total_loss += self.contrastive_weight * contrastive_loss
        else:
            contrastive_loss = torch.tensor(0.0, device=total_loss.device)

        return {
            'total_loss': total_loss,
            'level_losses': level_losses,
            'consistency_loss': consistency_loss,
            'contrastive_loss': contrastive_loss
        }

    def _compute_consistency_loss(self, logits_list: List[torch.Tensor],
                                targets_list: List[torch.Tensor]) -> torch.Tensor:
        """计算层次一致性损失"""
        if len(logits_list) <= 1:
            return torch.tensor(0.0, device=logits_list[0].device)

        consistency_loss = torch.tensor(0.0, device=logits_list[0].device)

        for i in range(1, len(logits_list)):
            parent_logits = logits_list[i-1]
            child_logits = logits_list[i]

            # 简化的一致性约束：使用MSE损失而不是KL散度
            # 这样可以避免维度不匹配的问题
            parent_probs = F.softmax(parent_logits, dim=-1)
            child_probs = F.softmax(child_logits, dim=-1)

            # 使用最小维度进行比较
            min_dim = min(parent_probs.size(-1), child_probs.size(-1))

            if min_dim > 0:
                parent_truncated = parent_probs[:, :min_dim]
                child_truncated = child_probs[:, :min_dim]

                # 归一化
                parent_truncated = parent_truncated / (parent_truncated.sum(dim=-1, keepdim=True) + 1e-8)
                child_truncated = child_truncated / (child_truncated.sum(dim=-1, keepdim=True) + 1e-8)

                # 使用MSE损失
                mse_loss = F.mse_loss(child_truncated, parent_truncated)
                consistency_loss = consistency_loss + mse_loss

        return consistency_loss